import request from '@/utils/request'


/**
 * 2022/3/1
 */
// 查询机构部门树
export function instDeptTree () {
  return request({
    url: '/api/admin/sys/dept/tree',
    method: 'get'
  })
}

// 查询机构树
export function instTree (type) {
  return request({
    url: '/api/admin/sys/organ/tree/'+type,
    method: 'get'
  })
}
/**
 * 2022/7/21
 */
// 查询机构部门树子节点
export function instDeptTreeChild (data) {
  return request({
    url: '/api/admin/sys/organ/child/nodes/tree',
    method: 'post',
    data: data
  })
}
// 6.41.查询未注册的机构信息
export function instUnregister (data) {
  const {brhCode = '', brhShtName ='',currentPage=1,pageSize=10} = data ||{}
  return request({
    url: `/api/admin/inst-sync/unregister/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      brhCode,
      brhShtName
    }
  })
}
// 6.42.注册机构
export function instRegister (data) {
  return request({
    url: '/api/admin/inst-sync/actions/register',
    method: 'post',
    data: data
  })
}