<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}"
    :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
      </router-link>
    </transition>
  </div>
</template>

<script>
  import logoImgLight from '@/assets/logo/logo1.png'
  import logoImgDark from '@/assets/logo/logo2.png'
  import variables from '@/assets/styles/variables.scss'
  const logoImgs = {
    'theme-dark': logoImgLight,
    'theme-light': logoImgDark
  }
  export default {
    name: 'SidebarLogo',
    props: {
      collapse: {
        type: Boolean,
        required: true
      }
    },
    computed: {
      variables() {
        return variables;
      },
      sideTheme() {
        return this.$store.state.settings.sideTheme
      }
    },
    watch: {
      sideTheme: {
        handler(value) {
          if (value) {
            this.logo = logoImgs[value]
          }
        },
        immediate: true
      }
    },
    data() {
      return {
        title: '管理系统',
        logo: logoImgLight
      }
    }
  }
</script>

<style lang="scss" scoped>
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-logo-container {
    position: relative;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #2b2f3a;
    text-align: center;
    overflow: hidden;

    & .sidebar-logo-link {
      height: 100%;
      width: 100%;

      & .sidebar-logo {
        width: 200px;
        height: 40px;
        vertical-align: middle;
        margin-right: 12px;
      }

      & .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 50px;
        font-size: 14px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-left: 7px;
        margin-right: 0;
      }
    }
  }
</style>