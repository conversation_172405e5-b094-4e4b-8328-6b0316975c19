package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于排查逻辑删除问题
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/debug")
@Api(tags = "调试接口")
public class DebugController {

    @Resource
    private ITbZjjgAcctInfoService tbZjjgAcctInfoService;

    @ApiOperation(value = "检查数据状态分布", notes = "查看数据库中oth_msg1_tx字段的值分布")
    @GetMapping("/checkDataStatus")
    public RestResponse<Map<String, Object>> checkDataStatus() {
        try {
            // 查询所有记录的状态分布
            List<TbZjjgAcctInfo> allRecords = tbZjjgAcctInfoService.list();
            
            Map<String, Integer> statusCount = new HashMap<>();
            Map<String, Object> result = new HashMap<>();
            
            for (TbZjjgAcctInfo record : allRecords) {
                String status = record.getOthMsg1Tx();
                String statusKey;
                
                if (status == null) {
                    statusKey = "NULL";
                } else if (status.isEmpty()) {
                    statusKey = "EMPTY";
                } else if ("0".equals(status)) {
                    statusKey = "ZERO";
                } else if ("1".equals(status)) {
                    statusKey = "ONE";
                } else {
                    statusKey = "OTHER:" + status;
                }
                
                statusCount.put(statusKey, statusCount.getOrDefault(statusKey, 0) + 1);
            }
            
            result.put("totalRecords", allRecords.size());
            result.put("statusDistribution", statusCount);
            
            // 查询前5条记录的详细信息
            List<TbZjjgAcctInfo> sampleRecords = tbZjjgAcctInfoService.lambdaQuery()
                    .orderByAsc(TbZjjgAcctInfo::getCpabAccId)
                    .last("LIMIT 5")
                    .list();
            
            result.put("sampleRecords", sampleRecords);
            
            log.info("数据状态检查完成，总记录数：{}，状态分布：{}", allRecords.size(), statusCount);
            
            return RestResponse.success(result);
        } catch (Exception e) {
            log.error("检查数据状态异常", e);
            return RestResponse.fail("检查数据状态失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "测试状态查询", notes = "测试不同状态值的查询结果")
    @GetMapping("/testStatusQuery")
    public RestResponse<Map<String, Object>> testStatusQuery(@RequestParam(required = false) String status) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试不同状态的查询
            long nullOrNot1Count = tbZjjgAcctInfoService.lambdaQuery()
                    .and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
                            .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1"))
                    .count();
            
            long equalTo1Count = tbZjjgAcctInfoService.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .count();
            
            long nullCount = tbZjjgAcctInfoService.lambdaQuery()
                    .isNull(TbZjjgAcctInfo::getOthMsg1Tx)
                    .count();
            
            long notEqualTo1Count = tbZjjgAcctInfoService.lambdaQuery()
                    .ne(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .count();
            
            result.put("nullOrNot1Count", nullOrNot1Count);
            result.put("equalTo1Count", equalTo1Count);
            result.put("nullCount", nullCount);
            result.put("notEqualTo1Count", notEqualTo1Count);
            
            if (status != null) {
                List<TbZjjgAcctInfo> records;
                if ("1".equals(status)) {
                    records = tbZjjgAcctInfoService.lambdaQuery()
                            .eq(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                            .last("LIMIT 10")
                            .list();
                } else {
                    records = tbZjjgAcctInfoService.lambdaQuery()
                            .and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
                                    .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1"))
                            .last("LIMIT 10")
                            .list();
                }
                result.put("queryRecords", records);
            }
            
            log.info("状态查询测试完成，参数：{}，结果：{}", status, result);
            
            return RestResponse.success(result);
        } catch (Exception e) {
            log.error("测试状态查询异常", e);
            return RestResponse.fail("测试状态查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "快速测试状态查询", notes = "快速测试当前的状态查询逻辑")
    @GetMapping("/quickTest")
    public RestResponse<Map<String, Object>> quickTest() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 检查是否有状态为'1'的记录
            long deletedCount = tbZjjgAcctInfoService.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .count();

            result.put("deletedRecordsCount", deletedCount);

            // 2. 如果没有，创建一条测试数据
            if (deletedCount == 0) {
                TbZjjgAcctInfo testRecord = tbZjjgAcctInfoService.lambdaQuery()
                        .last("LIMIT 1")
                        .one();

                if (testRecord != null) {
                    boolean updateResult = tbZjjgAcctInfoService.lambdaUpdate()
                            .eq(TbZjjgAcctInfo::getCpabAccId, testRecord.getCpabAccId())
                            .eq(TbZjjgAcctInfo::getAcctNm, testRecord.getAcctNm())
                            .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                            .update();

                    result.put("testDataCreated", updateResult);
                    result.put("testAccount", testRecord.getCpabAccId());
                    result.put("testAccountName", testRecord.getAcctNm());
                }
            }

            // 3. 重新检查删除记录数量
            deletedCount = tbZjjgAcctInfoService.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .count();

            result.put("finalDeletedCount", deletedCount);

            return RestResponse.success(result);
        } catch (Exception e) {
            log.error("快速测试异常", e);
            return RestResponse.fail("快速测试失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "创建测试数据", notes = "创建一条已删除状态的测试数据")
    @PostMapping("/createTestData")
    public RestResponse<String> createTestData() {
        try {
            // 查找一条正常记录
            TbZjjgAcctInfo normalRecord = tbZjjgAcctInfoService.lambdaQuery()
                    .and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
                            .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1"))
                    .last("LIMIT 1")
                    .one();
            
            if (normalRecord == null) {
                return RestResponse.fail("没有找到正常状态的记录");
            }
            
            // 更新为删除状态
            boolean result = tbZjjgAcctInfoService.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getCpabAccId, normalRecord.getCpabAccId())
                    .eq(TbZjjgAcctInfo::getAcctNm, normalRecord.getAcctNm())
                    .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .update();
            
            if (result) {
                log.info("创建测试数据成功，账户：{}，户名：{}", normalRecord.getCpabAccId(), normalRecord.getAcctNm());
                return RestResponse.success("创建测试数据成功，账户：" + normalRecord.getCpabAccId());
            } else {
                return RestResponse.fail("创建测试数据失败");
            }
        } catch (Exception e) {
            log.error("创建测试数据异常", e);
            return RestResponse.fail("创建测试数据失败：" + e.getMessage());
        }
    }
}
