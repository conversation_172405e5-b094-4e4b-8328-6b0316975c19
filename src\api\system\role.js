import request from '@/utils/request'

/**
 * 2022/3/2 liyang
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query 
 */
export function listRole (data) {
  const { roleId = "", roleName = "", status = "", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/roles/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      roleId,
      roleName,
      status
    }
  })
}

// 查询角色详细
export function getRole (roleId) {
  return request({
    url: '/system/role/' + roleId,
    method: 'get'
  })
}

// 新增角色
export function addRole (data) {
  return request({
    url: '/api/admin/roles/actions/add',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole (data) {
  return request({
    url: '/api/admin/roles/actions/edit',
    method: 'post',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus (roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除角色
export function delRole (roleId) {
  return request({
    url: '/api/admin/roles/actions/remove/' + roleId,
    method: 'post'
  })
}

// 查询角色已授权用户列表
export function allocatedUserList (query) {
  const { roleId = "", userName = "", userNickname = "", userStat = '', currentPage = 1, pageSize = 10 } = query || {}
  return request({
    url: `/api/admin/roles/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      roleId,
      userName,
      userNickname,
      userStat
    }
  })
}
// 查询角色未授权用户列表
export function unocatedUserList (query) {
  const { roleId = "", userName = "", userNickname = "", userStat = '', currentPage = 1, pageSize = 10 } = query || {}
  return request({
    url: `/api/admin/roles/unauth-users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      roleId,
      userName,
      userNickname,
      userStat
    }
  })
}

// 保存角色下的用户
export function addRoleUserList (query) {
  return request({
    url: '/api/admin/roles/users/actions/add',
    method: 'post',
    data: query
  })
}
// 删除角色下用户
export function delRoleUserList (data) {
  return request({
    url: '/api/admin/roles/users/actions/remove',
    method: 'post',
    data: data
  })
}
// 查询菜单资源树
export function roleTreeList (roleId) {
  return request({
    url: `/api/admin/roles/actions/get/res/tree/${roleId}`,
    method: 'get',
  })
}

/**
 * 2022/4/6
 * 5.32.查询角色菜单树接口
 * 请求地址中拼接角色编号参数
  /api/admin/roles/menu/tree/actions/get/{roleId}
  如：/api/admin/roles/menu/tree/actions/get/admin
 * @param {* 角色Id} roleId 
 */
export function getRoleMenuTreeList (roleId) {
  return request({
    url: `/api/admin/roles/menu/tree/actions/get/${roleId}`,
    method: 'get',
  })
}
/**
 * 2022/4/6
 * 5.33.查询角色菜单下按钮信息接口
 * @param {*menuId 资源Id roleId 角色ID} params 
 */
export function getRoleMenuTreeActionsList (params) {
  return request({
    url: `/api/admin/roles/menu/funcs/actions/get`,
    method: 'post',
    data: params
  })
}

/**
 * 2022/4/6
 * 5.36.保存角色按钮接口 --保存当前角色配置的按钮信息
 * roleId	角色编号
  menuId	菜单id
  funcList(功能按钮列表)
  id	菜单id
  funcName	功能按钮名称
 * @param {*} params 
 */
export function saveRoleMenuTreeActions (params) {
  return request({
    url: `/api/admin/roles/menu/funcs/actions/add`,
    method: 'post',
    data: params
  })
}
/**
 * 2022/4/6
 * 5.35.保存角色菜单接口 -- 保存当前角色配置的菜单信息
 * roleId	角色编号
 * menuList(菜单列表)
    id	菜单id	string	Y	
    menuName	菜单名称	string	N	
 * @param {*} params 
 */
export function saveRoleMenuActions (params) {
  return request({
    url: `/api/admin/roles/menus/actions/add`,
    method: 'post',
    data: params
  })
}

//查询当前角色下资源
export function getRoleList (roleId) {
  return request({
    url: `/api/admin/roles/actions/get/${roleId}/res`,
    method: 'get',
  })
}

// 保存角色资源信息
export function editRoleList (data) {
  return request({
    url: '/api/admin/roles/actions/add/res',
    method: 'post',
    data: data
  })
}

//5.20.查询用户敏感信息
export function usersSensitive (userName,sensitiveStatus) {
  return request({
    url: `/api/admin/users/sensitive/actions/get/${userName}/${sensitiveStatus}`,
    method: 'get',
  })
}


// 批量取消用户授权角色
export function authUserCancelAll (data) {
  return request({
    url: '/system/role/authUser/cancelAll',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll (data) {
  return request({
    url: '/system/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}