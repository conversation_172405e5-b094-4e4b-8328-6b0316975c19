export default {
    index: true,
    indexLabel: '序号',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增子业务参数',
    viewTitle: '查看子业务参数',
    editTitle: '修改子业务参数',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: true,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    menuWidth: 130,
    column: [
      {
        label: '业务代码',
        prop: 'opeCd',
        rules: [{
          required: true,
          message: "业务代码不能为空",
          trigger: "blur"
        }]
      },
      {
        label: '委托单位代码',
        prop: 'merchId',
        width: 120,
        rules: [{
          required: true,
          message: "委托单位代码不能为空",
          trigger: "blur"
        }]
      },
      {
        label: '子业务代码',
        prop: 'subOpeId',
        width: 120,
        rules: [{
          required: true,
          message: "子业务代码不能为空",
          trigger: "blur"
        }]
      },
      {
        label: '子业务名称',
        prop: 'subOpeNm',
        width: 120,
        rules: [{
          required: true,
          message: "子业务名称不能为空",
          trigger: "blur"
        }]
      },
      {
        label: 'SP商户代码',
        prop: 'spMerchId',
        width: 120,
      },
      {
        label: '商家代码',
        prop: 'merchInstId',
        width: 120,
      },
      {
        label: '行业机构代码',
        prop: 'busiMerchId',
        width: 120,
      },
      {
        label: '账单代码',
        prop: 'billId'
      },
      {
        label: '开办日期',
        prop: 'openDt',
        width: 120,
        type: 'date',
        valueFormat: 'yyyyMMdd'
      },
      {
        label: '摘要名称',
        prop: 'summCd'
      },
      {
        label: '公司摘要',
        prop: 'cpcbSummCd'
      },
      {
        label: '代收频率',
        prop: 'payLimitNum',
        tip: '代收频率】6位字符串，同银联报文定义：扣款时间单位（2字节）+扣款次数（4字节），1、扣款时间单位:（1）00：不限制代收频率（2）01：年（3）02：季（4）03：月（5）04：周（6）05：日（7）其他取值：保留使用2、扣款次数：表示扣款时间单位内允许的最大扣款次数（指成功交易次数）。当扣款时间单位为000000（不限制代收频率）时，表示不限制扣款次数，发卡方可忽略扣款次数。例如：每月最多可扣款10次，则扣款时间单位为03（月），扣款次数为0010，代收频率取值为030010。',
        type: 'number'
      },
      {
        label: '最小交易金额',
        width: 120,
        prop: 'minPayAt',
        type: 'number'
      },
      {
        label: '最大交易金额',
        width: 120,
        prop: 'maxPayAt',
        type: 'number'
      },
      {
        label: '委托关系期限',
        width: 120,
        prop: 'dueTerm',
        type: 'number'
      },
      {
        label: '委托业务单位开关',
        width: 140,
        prop: 'subMerchOnOffFg',
        type: 'select',
        dicData: [
          {
            label: '开通',
            value: '1'
          },
          {
            label: '关闭',
            value: '0'
          }
        ]
      },
      {
        label: '白名单标志',
        prop: 'whiteListFg',
        width: 120,
        type: 'select',
        dicData: [
          {
            label: '白名单商户',
            value: '1'
          },
          {
            label: '非白名单商户',
            value: '0'
          }
        ]
      },
      
      {
        label: '滞纳金比例',
        prop: 'delayRate',
        width: 120,
        type: 'number'
      },
      {
        label: '子业务开通标志',
        prop: 'flag01',
        width: 120,
        tip: '【子业务开通标志】第1位是否提供发票打印,第2位是否提供缴费业务,第3位是否柜面签约',
        rules: [{
          required: true,
          message: "子业务开通标志不能为空",
          trigger: "blur"
        }]
      }
    ]
  } 