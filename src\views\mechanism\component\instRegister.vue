<template>
  <!-- 授权用户 -->
  <el-dialog
    :visible.sync="visible"
    append-to-body
    :style="styleName"
    width="60%"
    :class="fullscreen?'yo-dialog yo-table__dialog yo-dialog--fullscreen':'yo-dialog '"
  >
    <div slot="title" class="yo-table__dialog__header">
      <span class="el-dialog__title">机构注册</span>
      <div class="yo-table__dialog__menu">
        <i
          @click="handleFullScreen"
          :class="fullscreen?'el-icon-news':'el-icon-full-screen'"
          class="el-dialog__close"
        ></i>
      </div>
    </div>
    <yo-table
      ref="crud"
      :data="instList"
      :option="option"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:inst:unregister:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
      </template>
    </yo-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :size="option.size || 'small'" @click="handleSelectUser">保 存</el-button>
      <el-button :size="option.size || 'small'" @click="cancelSelectUser">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { instUnregister, instRegister } from "@/api/system/dept";

export default {
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      formParent: {},
      fullscreen: false,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      option: {
        selection: true,
        align: "center",
        card: true,
        menuAlign: "center",
        emptyBtnText: "重置",
        emptyBtnIcon: "el-icon-refresh",
        searchMenuSpan: 8,
        menu: false,
        searchBtn: false,
        emptyBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        columnBtn: false,
        refreshBtn: false,
        tip: false,
        searchShowBtn: false,
        column: [
          {
            label: "机构编号",
            prop: "brhCode",
            search: true,
            placeholder: '请输入'
          },
          {
            label: "机构简称",
            prop: "brhShtName",
            search: true,
            placeholder: '请输入'
          },
          {
            label: "上级机构编号",
            prop: "upBrhCode"
          },
          {
            label: "上级机构简称",
            prop: "upBrhShtName"
          },
          {
            label: "机构层级",
            prop: "brhLvl",
            // type: "select",
            dicData: [
              {
                label: "总行",
                value: "01"
              },
              {
                label: "一级分行",
                value: "02"
              },
              {
                label: "二级分行",
                value: "03"
              },
              {
                label: "一级支行",
                value: "04"
              },
              {
                label: "二级支行",
                value: "05"
              }
            ]
          },
          {
            label: "机构地址",
            prop: "brhAddr"
          }
        ]
      },
      // 遮罩层
      visible: false,
      // 选中数组值
      instIds: [],
      // 授权用户数据
      instList: []
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: "15%", bottom: "5%" };
      } else {
        return { top: 0, bottom: 0, marginTop: 0 };
      }
    }
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    // 显示弹框
    show() {
      setTimeout(() => {
        this.getList();
      }, 500);
      this.visible = true;
      this.fullscreen = false;
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 刷新按钮
    refresh(val) {
      this.handleQuery();
    },
    // 点击搜索
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.instIds = selection
    },
    // 查询表数据
    getList(params) {
      instUnregister({ ...params, ...this.page }).then(res => {
        let list = res.data.list;
        if(list.upInst){
          list.forEach(item => {
          item.upBrhCode = item.upInst["brhCode"];
          item.upBrhShtName = item.upInst["brhShtName"];
        });
        }
        const data = res.data;
        this.instList = data.list;
        this.page.total = data.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 选择机构保存 */
    handleSelectUser() {
      if (this.instIds.length) {
        instRegister(this.instIds).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.visible = false;
          this.$emit("ok");
        }
      });
      }else{
        this.$modal.msgError("请选择要注册的机构");
      }
    },
    cancelSelectUser() {
      this.visible = false;
      this.fullscreen = false;
    },
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
</style>