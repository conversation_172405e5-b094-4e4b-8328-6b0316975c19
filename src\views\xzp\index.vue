<!--监管账户列表-->
<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @row-update="rowUpdate" @row-save="rowSave" @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>
      <!-- 自定义列 -->
      <template slot="updateTimeSearch">
        <el-date-picker v-model="search.updateTimeBox" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="截止日期" value-format="yyyyMMddHHmmss"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </template>

      <!-- 自定义显示隐藏列 -->
      <template slot="accountname" slot-scope="scope">
        <div>
          <span>{{scope.row.accountname}}</span>
          <el-button v-if="scope.row.accountname" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountname')"></el-button>
        </div>
      </template>
      <template slot="accountno" slot-scope="scope">
        <div>
          <span>{{scope.row.accountno}}</span>
          <el-button v-if="scope.row.accountno" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountno')"></el-button>
        </div>
      </template>

      <!-- 自定义左侧操作栏 -->
      <template slot="menuLeft">

        <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="handleAdd">新增</el-button>
        <!-- <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">批量删除</el-button> -->
      </template>


      <!-- 自定义右侧操作栏 -->
      <template slot="menuRight">
        <input id="fileslist" v-show="false" type="file" accept=".xls" ref="fileRef" @change="fileChange" />
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleImport('1')">
          导入
        </el-button>
        <!-- <el-button type="warning" icon="el-icon-upload2" size="mini" @click="handleImport('0')">
          全量导入
        </el-button> -->

        <el-button type="primary" icon="el-icon-download" size="mini" @click="exportExample">
          下载模板
        </el-button>

        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>

      </template>

      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-edit" @click.stop="editJgzh(row,index)">
          修改
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-delete" @click="handleDelete(row)">
          删除
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          查看
        </el-button>
      </template>
    </yo-table>

    <!-- <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
      <span slot="footer" class="dialog-footer">
        <el-button style="margin-right: 15px" type="primary" @click="dialogQueren(dialogType)" size="mini">
          确 定
        </el-button>
        <el-button @click="dialogVisible = false" size="mini">
          取 消
        </el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
import {
  addJgzhInfo,
  list,
  exportJgzh,
  downloadExample,
  importJgzh,
  importJgzhAdd,
  editJgzh,
  editJgzhInfo,
  viewJgzh,
  batchUpdateJgzh,
  delJgzh,
  delJgzhs,
  querySensitive
} from '@/api/jgzh';
import jgzhOption from './infoData/jgzhOption.js';
import Cookies from "js-cookie";

export default {
  name: 'jgzh',
  components: {},
  dicts: [
    "xzp_exe_type",
    "xzp_jgzh_status"
  ],
  data () {
    return {
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: jgzhOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    'search.updateTimeBox': {
      handler (val) {
        if (val) {
          this.search.updateTimeStart = val[0];
          this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "executetype",
      this.dict.type["xzp_exe_type"]
    );
    this.updateDictData(
      this.option.column,
      "jgzhStatus",
      this.dict.type["xzp_jgzh_status"]
    );

    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 更新字典数据
    updateDictData (option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 新增表单保存
    async rowSave (form, done, loading) {
      console.log("新增表单参数==>" + JSON.stringify(form))
      addJgzhInfo(form).then(res => {
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await list(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.id).join(',');
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },

    /** 新增按钮操作 */
    handleAdd () {
      this.deptName = '';
      this.instName = '';
      this.$refs.crud.rowAdd();
    },

    /** 导出结果，处理返回的流文件 */
    handleExportData (res) {
      console.log('res:', res);
      if (!res) return;
      let data = res.data;
      let filename = res.headers['content-disposition'].split('=')[1];
      let _filename = decodeURI(filename);
      const link = document.createElement('a');
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' });
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 下载模板 */
    exportExample () {
      downloadExample().then(res => {
        this.handleExportData(res);
      });
    },
    /** 导出按钮操作 */
    handleExport () {
      const data = { userIds: this.ids, ...this.exportSearch };
      // console.log('导出数据：', this.search);
      exportJgzh(this.search).then(res => {
        // console.log('导出的返回数据：', res);
        this.handleExportData(res);
      });
    },

    fileChange (event) {
      console.log("监管账户导入文件变化filechange--" + JSON.stringify(event));
      this.loading = true;
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append('file', file);
      console.log("按钮属性---" + this.buttonAttr);
      if (this.buttonAttr == '0') {
        console.log("全量导入---->");
        importJgzh(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("全量导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("全量导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else if (this.buttonAttr == '1') {
        console.log("增量导入---->");
        importJgzhAdd(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("增量导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("增量导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else {
        console.log("未知按钮属性，无导入操作");
      }
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport (event) {
      console.log("handleImport--" + event);
      this.buttonAttr = event;
      if (this.buttonAttr == '0') { //全量导入
        this.$confirm("此操作将会清空表数据，确认是否全量导入？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});
      } else if (this.buttonAttr == '1') { //增量导入
        this.$confirm("此操作只新增和更新部分数据，确认是否增量导入？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});

      } else {
        console.log("异常情况，未获取到按钮事件类型:" + this.buttonAttr);
      }


    },
    //过滤列表数据
    filterData (data) {
      return data;
    },
    // 修改
    async editJgzh (row, index) {
      console.log("修改信息：" + JSON.stringify(row));
      var status = row.jgzhStatus;
      if (status == "1") { //1-审核通过状态，3-反馈成功状态，不可编辑
        this.$confirm("反馈信息已审核通过，待反馈，不可修改！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {

          })
          .catch(() => {});
      } else if (status == "2") { //1-审核不通过，请新增审核
        this.$confirm("反馈信息审核不通过，修改后将重新提交审核！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {
            //进入编辑
            var id = row.id;
            viewJgzh(id).then(res => {
              //console.log("查询返回的结果--" + JSON.stringify(res))
              if (res.code == 0) {
                this.$refs.crud.rowEdit(res.data);
              }
            });

          })
          .catch(() => {});
      } else if (status == "3") { //1-审核通过状态，3-反馈成功状态，不可编辑
        this.$confirm("反馈信息已反馈成功，不可修改！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {})
          .catch(() => {});
      } else if (status == "4") { //反馈失败
        this.$confirm("信息反馈失败，将在下个批次重新反馈！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {
            //alert("点击确认，更新状态为待反馈");
            //进入编辑
            /* var res = {};
             res.id = row.id;
             res.jgzhStatus = "1";//待反馈
             editJgzhInfo(res).then(response => {
               done()
               if (response.code == 0) {
                 this.$message.success(response.message);
               }
             }); */

          })
          .catch(() => {

          }).finally(() => {
            this.handleQuery();
          });
      } else {
        //进入编辑
        var id = row.id;
        viewJgzh(id).then(res => {
          //console.log("查询返回的结果--" + JSON.stringify(res))
          if (res.code == 0) {
            this.$refs.crud.rowEdit(res.data);
          }
        });

        // var res = {};
        // res.id = row.id
        // res.serialno = row.serialno;
        // res.bankid = row.bankid;
        // res.accountname = row.accountname;
        // res.accountno = row.accountno;
        // res.executetype = row.executetype;
        // res.executeamount = row.executeamount;
        // res.executedept = row.executedept;
        // res.executedate = row.executedate;
        // res.releaseserialno = row.releaseserialno;
        // res.releasetime = row.releasetime;
        // res.note = row.note;

        // Cookies.set("serialno", row.serialno);
        // Cookies.set("bankid", row.bankid);
        // Cookies.set("accountname", row.accountname);
        // Cookies.set("accountno", row.accountno);
        // Cookies.set("executetype", row.executetype);
        // Cookies.set("executedept", row.executedept);
        // Cookies.set("executeamount", row.executeamount);
        // Cookies.set("executedate", row.executedate);
        // Cookies.set("releaseserialno", row.releaseserialno);
        // Cookies.set("releasetime", row.releasetime);
        // Cookies.set("note", row.note);
        // this.$refs.crud.rowEdit(res, index);



      }

    },
    //查看
    async handleView (row) {
      this.$refs.crud.rowView(row);
      /* var id = row.id;
       viewJgzh(id).then(res => {
         console.log("查询返回的结果--" + JSON.stringify(res))
         if (res.code == 0) {
           this.$refs.crud.rowView(res.data);
         }
       }); */
    },

    // 修改表单保存
    rowUpdate (form, index, done, loading) {
      editJgzh(form).then(response => {
        done()
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
        }
      });
    },

    //批量修改
    async batchUpdateJgzh (row) {
      const h = this.$createElement;
      this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h('p', { style: 'word-break: break-all' },
              '是否批量修改选中数据状态？'
            ),
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
        .then(async () => {
          let res;
          if (row.id != undefined) {
            res = await batchUpdateJgzh({ id: row.id });
          } else {
            if (
              this.ids != null &&
              this.ids != '' &&
              this.ids != undefined
            ) {
              res = await batchUpdateJgzh({ id: this.ids });
            }
          }
          this.getList(this.search);
        })
        .catch(() => {});
    },

    /** 删除按钮操作 */
    handleDelete (row) {
      const id = row.id || this.id;
      const serialno = row.serialno;
      const status = row.jgzhStatus;
      const h = this.$createElement;
      if(status=="1"||status=="2"){
        this.$confirm("该记录已审核，不可删除！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {})
          .catch(() => {});
      }else if(status=="3"||status=="4"){
        this.$confirm("该记录已反馈，不可删除！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {})
          .catch(() => {});
      }else{
        this.$confirm(`是否永久删除执行流水号为" ${serialno} "的数据项？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            showCancelButton: true,
          }).then(async () => {
            // 删除单条记录
            if (row.id) {
              console.log("删除单个监管账户信息----")
              this.handleDelJgzh(id, delJgzh);
            } else {
              console.log("批量删除监管账户信息----")
              this.handleDelJgzh(id, delJgzhs);
            }
          })
          .catch(() => {});
      }

    },

    async handleDelJgzh (id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.$refs.crud.toggleSelection();
        this.getList();
      }
    },

    /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.id, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("监管账户敏感信息响应结果：" + res);
        this.data.forEach(item => {
          if (item.id == data.id) {
            item[type] = res[type];
          }
        });
      });
    },
    // end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}
</style>
