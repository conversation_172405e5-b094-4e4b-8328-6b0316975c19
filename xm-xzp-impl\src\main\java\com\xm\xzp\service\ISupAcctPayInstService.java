package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.SupAcctPayInst;
import com.xm.xzp.model.vo.SupAcctPayInstVo;

public interface ISupAcctPayInstService extends IService<SupAcctPayInst> {
    /**
     * 分页查询监管账户支付指令
     *
     * @param supAcctPayInstVo 查询条件
     * @param pageNum       当前页
     * @param pageSize      每页数量
     * @return 分页结果
     */
    PageInfo<SupAcctPayInst> supAcctPayInstList(SupAcctPayInstVo supAcctPayInstVo, Integer pageNum, Integer pageSize);

}
