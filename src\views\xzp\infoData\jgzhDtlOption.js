// 监管账户信息列表、弹框项
import validate from '@/utils/validate';
export default {
  index: true,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '新增监管账户信息',
  viewTitle: '监管账户信息查询',
  editTitle: '修改监管账户信息',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menuWidth: 100,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '执行流水号',
      prop: 'serialno',
      slot: true,
      formslot: true, //编辑需要
      showColumn: false,
      editDisabled: true, //不可修改
      span: 24, //一行
      addDisplay: true,

      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      // searchSpan: 6,//右边长度
      rules: [{
        required: true,
        message: "请输入执行流水号",
        trigger: "change"
      }],
    },
    {
      label: '开户银行代码',
      prop: 'bankid',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '账户名',
      prop: 'accountname',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '账户号',
      prop: 'accountno',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '执行类型',
      prop: 'executetype',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24
    },
    {
      label: '执行部门',
      prop: 'executedept',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24
    },
    {
      label: '执行时间',
      prop: 'executedate',
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      rules: [{
        required: true,
        message: "请输入执行时间",
        trigger: "change"
      }],
    },

    {
      label: '解除执行原流水号',
      prop: 'releaseserialno',
      width: 180,
      formslot: true,
      showColumn: false,
      editDisabled: true,
      // labelWidth: 180,
      span: 24,
      addDisplay: true,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      rules: [{
        required: true,
        message: "请输入解除执行原流水号",
        trigger: "change"
      }],
    },
    {
      label: '解除执行时间',
      prop: 'releasetime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '备注说明',
      prop: 'note',
      width: 180,
      slot: true,
      // formslot: true,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '创建人',
      prop: 'createBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '操作人',
      prop: 'updateBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '操作时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      dicData: [],
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
    },
    {
      label: '预留字段',
      prop: 'reserve',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段1',
      prop: 'reserveA',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段2',
      prop: 'reserveB',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段3',
      prop: 'reserveC',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段4',
      prop: 'reserveD',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
  ],
};
