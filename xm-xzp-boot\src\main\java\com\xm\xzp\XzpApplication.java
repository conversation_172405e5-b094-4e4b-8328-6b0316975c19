package com.xm.xzp;

import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableSwagger2
//@EnableDiscoveryClient // 开启服务发现
public class XzpApplication {

    public static void main(String[] args) {
        SpringApplication.run(XzpApplication.class, args);
    }

}
