<template>
  <div class="dashboard-editor-container">

    <!-- 接入系统展示 -->
    <el-tabs type="border-card">
      <el-tab-pane label="常用系统">
        <el-row :gutter="20" >

          <!-- 1.返回邮连图标显示（特殊处理） -->
          <el-col :span="4" v-if="uepsArea!=null" >
            <div class="common-system-content" @click="returnUnified">
              <span class="common-system-round el-avatar" >
                <img src="@/assets/joinSystem_images/unified_icon.png" style="object-fit: cover;">
              </span>
              <div class="common-system-name my-text">返回邮连</div>
            </div>
          </el-col>

          <!-- 2.新一代综管考评专用（特殊处理） -->
            <!-- 说明是新一代综管系统，则需再展示一个人员考评专用访问入口 -->
          <el-col :span="4" v-for="item in joinSystems"  v-if="item.sysNo=='350200001'">
            <!-- 当前网络类型与所属系统的访问类型匹配时，展示有色图标和文字，并可点击跳转 -->
            <div class="common-system-content" @click="goSystem(item,true)"
                 v-if="(curNetworkFlag=='nw') && (item.accessNetworkType=='1' || item.accessNetworkType=='3')
                    || (curNetworkFlag=='ww') && (item.accessNetworkType=='2' || item.accessNetworkType=='3')">
              <span class="common-system-round el-avatar" >
                <img src="@/assets/joinSystem_images/default_icon.png" style="object-fit: cover;">
              </span>
              <div class="common-system-name">{{item.sysName}}</div>
              <div class="common-system-name">(人员考评专用)</div>
            </div>

            <!-- 当前网络类型与所属系统的访问类型不匹配时，展示灰色图标和文字，不可点击跳转 -->
            <div class="common-system-content"
                 v-if="(curNetworkFlag=='nw') && (item.accessNetworkType=='2' || item.accessNetworkType=='4')
                    || (curNetworkFlag=='ww') && (item.accessNetworkType=='1' || item.accessNetworkType=='4')">
              <span class="common-system-round el-avatar" >
                <img src="@/assets/joinSystem_images/default_icon_gray.png" style="object-fit: cover;">
              </span>
              <div class="common-system-name">{{item.sysName}}</div>
              <div class="common-system-name">(人员考评专用)</div>
            </div>
          </el-col>

          <!-- 3.接入系统图标、名称显示（通用） -->
          <el-col :span="4" v-for="item in joinSystems" :key="item.sysNo">
            <!-- 当前网络类型与所属系统的访问类型匹配时，展示有色图标和文字，并可点击跳转 -->
            <div class="common-system-content" @click="goSystem(item, false)"
                 v-if="(curNetworkFlag=='nw') && (item.accessNetworkType=='1' || item.accessNetworkType=='3')
                    || (curNetworkFlag=='ww') && (item.accessNetworkType=='2' || item.accessNetworkType=='3')">
              <span class="common-system-round el-avatar" >
                <img src="@/assets/joinSystem_images/default_icon.png" style="object-fit: cover;">
              </span>
              <div class="common-system-name my-text">{{item.sysName}}</div>
            </div>

            <!-- 当前网络类型与所属系统的访问类型不匹配时，展示灰色图标和文字，不可点击跳转 -->
            <div class="common-system-content"
                 v-if="(curNetworkFlag=='nw') && (item.accessNetworkType=='2' || item.accessNetworkType=='4')
                    || (curNetworkFlag=='ww') && (item.accessNetworkType=='1' || item.accessNetworkType=='4')">
              <span class="common-system-round el-avatar" >
                <img src="@/assets/joinSystem_images/default_icon_gray.png" style="object-fit: cover;">
              </span>
              <div class="common-system-name">{{item.sysName}}</div>
            </div>
          </el-col>

        </el-row>
      </el-tab-pane>
    </el-tabs>


<!--    <panel-group @handleSetLineChartData="handleSetLineChartData" />

    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="lineChartData" />
    </el-row>

    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <raddar-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <pie-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col>
    </el-row>-->


  </div>
</template>

<script>
  import PanelGroup from './dashboard/PanelGroup'
  import LineChart from './dashboard/LineChart'
  import RaddarChart from './dashboard/RaddarChart'
  import PieChart from './dashboard/PieChart'
  import BarChart from './dashboard/BarChart'
  import {queryJoinSysList, getCurUserToken, getDictEntry, requestNull} from '../api/tyjr'

  const lineChartData = {
    newVisitis: {
      expectedData: [100, 120, 161, 134, 105, 160, 165],
      actualData: [120, 82, 91, 154, 162, 140, 145]
    },
    messages: {
      expectedData: [200, 192, 120, 144, 160, 130, 140],
      actualData: [180, 160, 151, 106, 145, 150, 130]
    },
    purchases: {
      expectedData: [80, 100, 121, 104, 105, 90, 100],
      actualData: [120, 90, 100, 138, 142, 130, 130]
    },
    shoppings: {
      expectedData: [130, 140, 141, 142, 145, 150, 160],
      actualData: [120, 82, 91, 154, 162, 140, 130]
    }
  }

  export default {
    name: 'Index',
    components: {
      PanelGroup,
      LineChart,
      RaddarChart,
      PieChart,
      BarChart
    },
    data() {
      return {
        lineChartData: lineChartData.newVisitis,
        joinSystems: [],
        channel: '',  //uaas或xmueps
        userToken: '',
        uepsArea: '',
        curNetworkFlag: '' //当前网络标识（nw:内网，ww:外网）
      }
    },
    created() {
      this.channel = localStorage.getItem('channel'); //若是邮连登录,则channel为uaas,否则为空(该值是在store/modules/user.js中set的)
      this.userToken = localStorage.getItem('userToken'); //若是邮连登录,则userToken为邮连的，否则为空(该值是在store/modules/user.js中set的)
      this.uepsArea = localStorage.getItem('uepsArea'); //若邮连配置为代理跳转，则跳转链接不带uepsArea参数，否则直连跳转会带该参数hfApp/lfApp(该值是在store/modules/user.js中set的)
      this.judgeCurNetworkFlag(); //判断内外网标识
      this.queryJoinSysData(); //查询接入系统数据
    },
    methods: {
      handleSetLineChartData(type) {
        this.lineChartData = lineChartData[type]
      },
      queryJoinSysData(){
        let data = {userToken: this.userToken, channel:this.channel};
        queryJoinSysList(data).then(res => {
          this.joinSystems = res.data;
        });
      },
      goSystem(item,empCheck) {
        //调用空请求(用于首页点击子系统链接前调该接口判断超时自动跳到登录页)
        requestNull().then(res => {
          //若上面这个接口请求检测到token超时，则弹出重新登录，不再执行下面内容
          if(item.loginAddr==null || item.loginAddr==""){
            this.$message.error("该系统未配置登录页地址！");
          }else{
            let userToken = localStorage.getItem('userToken'); //实时获取userToken，以防this.userToken是旧的
            let uri = item.loginAddr + "?userToken="+userToken;
            if(this.channel && this.channel == "uaas"){
              uri = uri + "&X-ER-LGT=uaas";
            } else {
              uri = uri + "&X-ER-LGT=xmueps";
            }

            if(empCheck==true){//说明是人员考评专用，则再加一个参数标识
              uri = uri + "&empCheck=true";
            }
            window.open(uri, '_blank');
            /*let data = {};
            getCurUserToken(data).then(res => {
              if (res.code == 0) {
                //this.$message.success(res.message);
                let userToken = res.message;
                let channel = localStorage.getItem("channel");
                let uri = item.loginAddr + "?userToken="+userToken;
                if(channel && channel == "uaas"){
                  uri = uri + "&X-ER-LGT=uaas";
                } else {
                  uri = uri + "&X-ER-LGT=xmueps";
                }
                window.open(uri, '_blank');
              }else{
                this.$message.error("token获取异常！");
              }
            });*/
          }
        });
      },
      //返回邮连
      returnUnified(){
        let url = "";
        let dictTypeId = 'tyjr_unified_config';
        let dictId = '';
        if(this.uepsArea == 'hfApp'){
          dictId = 'hfApp_url';
          getDictEntry(dictTypeId,dictId).then(res => {
            url = res.data.dictName + '?fallbackToken='+this.userToken;
            console.log("url===="+url)
            window.location.href = url  //当前页面打开
          });
        }else if(this.uepsArea == 'lfApp'){
          dictId = 'lfApp_url';
          getDictEntry(dictTypeId,dictId).then(res => {
            url = res.data.dictName + '?fallbackToken='+this.userToken;
            console.log("url===="+url)
            window.location.href = url  //当前页面打开
          });
        }else{ //否则，说明是代理跳转，获取地址栏的ip和端口
          url = window.location.protocol + '//' + window.location.host + '?fallbackToken='+this.userToken;
          console.log("url===="+url)
          window.location.href = url  //当前页面打开
        }
      },
      //判断当前网络标识
      judgeCurNetworkFlag(){
        if(this.channel=='xmueps' || (this.channel=='uaas' && (this.uepsArea == 'hfApp' || this.uepsArea == 'lfApp'))){
          this.curNetworkFlag = 'nw'; //说明在内网
        }else{
          this.curNetworkFlag = 'ww';  //否则在外网
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .dashboard-editor-container {
    padding: 32px;
    background-color: rgb(240, 242, 245);
    position: relative;

    .chart-wrapper {
      background: #fff;
      padding: 16px 16px 0;
      margin-bottom: 32px;
    }
  }

  @media (max-width:1024px) {
    .chart-wrapper {
      padding: 8px;
    }
  }

  /* 20240422新加样式：当鼠标悬停在 .my-text 上时的样式 */
  .my-text:hover {
    color: #0080ff; /* 文字颜色变为蓝色 */
    cursor: pointer; /* 光标变为手形 */
  }

  .common-system-content {
    text-align: center;
    width: 100%;
    margin: 10px 10px 10px 10px;
  }

  .common-system-round {
    background: #efefef;
    border-radius: 10px;
    cursor: pointer;
    margin-bottom: 5px;
  }

  .el-avatar {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    font-size: 14px;
    height:60px;
    width:60px;
    line-height:60px;
  }

  .common-system-name {
    font-size: 16px;
    line-height: 20px;
    //white-space: nowrap;
    overflow: hidden;
   // text-overflow: ellipsis;
    color: #333333;
  }
</style>
