import validate from "@/utils/validate"
export default {
  index:true,
  indexLabel:'序号',
  rowKey:"userId",
  reserveSelection:true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '新增用户',
  viewTitle: '查看用户',
  editTitle: '修改用户',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  tip:false,
  columnBtn:false,
  // excelBtn:true,
  column: [
    {
      label: '用户名',
      prop: 'userName',
      search: true,
      editDisabled: true,
      rules: [{
        required: true,
        message: "请输入用户名",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      {
        validator: validate.userIdValidate, trigger: 'blur'
      }]
    },
    {
      label: '姓名',
      prop: 'userNickname',
      search: true,
      rules: [{
        required: true,
        message: "请输入姓名",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      {
        validator: validate.blankSpace, trigger: 'blur'
      }]
    },
    {
      label: '性别',
      prop: 'gender',
      type: 'select',
      slot:true,
      dicData: [],
      rules: [{
        required: true,
        message: "请选择性别",
        trigger: "change"
      }]
    },
    {
      label: '人员状态',
      prop: 'empStatus',
      type: "select",
      search: true,
      slot: true,
      formslot: true,
      dicData: [],
      rules: [{
        required: true,
        message: "请选择人员状态",
        trigger: "change"
      }]
    },
    {
      label: '柜员号',
      prop: 'tellerId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '身份证号码',
      prop: 'idCard',
      slot:true,
      width:170,
      rules: [{
        required: true,
        message: "请输入身份证号",
        trigger: "blur"
      },
      {
        validator: validate.checkIdentity, trigger: 'blur'
      }]
    },
    {
      label: '手机号',
      prop: 'phoneNumber',
      slot:true,
      width:120,
      rules: [{
        required: true,
        message: "请输入手机号",
        trigger: "blur"
      },
      {
        validator: validate.telNumber, trigger: 'blur'
      }]
    },
    {
      label: '电子邮箱',
      prop: 'email',
      slot:true,
      width:200,
      rules: [{validator: validate.email, trigger: 'blur'}]
    },
    {
      label: '所属机构',
      prop: 'instId',
      formslot: true,
      dicData: [],
      hide: true,
      showColumn: false,
    },
    /*{
      label: '所属部门',
      prop: 'deptIds',
      formslot: true,
      dicData: [],
      hide: true,
      showColumn: false,
    },*/
    {
      label: '用户状态',
      prop: 'userStat',
      type: "select",
      search: true,
      slot: true,
      formslot: true,
      dicData: [],
      rules: [{
        required: true,
        message: "请选择用户状态",
        trigger: "change"
      }]
    },
    {
      label: '工作组',
      prop: 'groups',
      hide: true,
      showColumn: false,
      type: 'select',
      dicData: [],
      props: {
        label: 'groupName',
        value: 'groupId'
      },
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '岗位',
      prop: 'posts',
      addDisplay: false,
      editDisplay:false,
      hide: true,
      showColumn: false,
      type: 'select',
      dicData: [],
      props: {
        label: 'postName',
        value: 'postId'
      },
    },
    {
      label: '创建人',
      prop: 'createUser',
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '更新人',
      prop: 'updateUser',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '用户角色',
      prop: 'roleIds',
      type: 'select',
      hide: true,
      multiple: true,
      showColumn: false,
      filterable: true,
      dicData: [],
      props: {
        label: 'roleName',
        value: 'roleId'
      },
    }
  ]
}
