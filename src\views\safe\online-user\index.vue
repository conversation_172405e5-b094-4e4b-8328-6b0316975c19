<template>
  <div class="app-container">
    <yo-table
      v-loading="loading"
      :data="data"
      :option="recordOption"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
    >
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:online:users:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
      </template>
     
      <template slot-scope="{row,size,type,index}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-edit"
          @click.stop="handleConstraint(row)"
          v-hasPermi="['admin:online:users:offline']"
        >强制下线</el-button>
      </template>
    </yo-table>
  </div>
</template>
<script>
import { listRecord,insertingCoil } from "@/api/system/safe/onlineuser";
import recordOption from "./infoData/recordOption.js";
export default {
  name: "Online-User",
  data() {
    return {
      loading: true,
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      recordOption: recordOption,
      obj: {},
      deptIdsList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true
    };
  },
  created() {},
    // 首次加载调用此方法
    methods:{
       onLoad(page) {
      this.getList(this.search);
    },
   
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    // 刷新按钮
    refresh() {
      this.handleQuery();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    /** 查询用户列表 */
    async getList(query) {
      const params = { ...query, ...this.page };
      this.loading = true;
      let response = await listRecord(params);
      if (response.code == 0) {
        this.data = response.data.list ?response.data.list :[];
        this.page.total = response.data.total;
        this.loading = false;
      }
    },
    handleConstraint(row) {
       var query = {
        recordId: row.recordId,
        userName: row.userName,
        device: row.device
      };
      const userName = row.userName || this.userName;
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h(
            "p",
            { style: "word-break: break-all" },
            '是否强制"' + userName + '"下线？'
          )
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then( () => {
     
            insertingCoil(query).then(res => {
     
            this.$message.success(res.message);
          });

          
        })
        .catch(() => {});
    },

  }
    }
</script>