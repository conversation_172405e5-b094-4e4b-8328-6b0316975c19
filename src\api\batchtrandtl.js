import request from '@/utils/request'

// 批量交易明细查询
export function getBatchTranDtlList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/batchTranDtlList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  })
}

// 根据业务代码获取委托单位列表
export function listGroupByOpeCd(opeCd) {
  return request({
    url: '/api/admin/xzp/listGroupByOpeCd',
    method: 'get',
    params: { opeCd }
  })
}

// 根据委托单位获取业务代码列表
export function listGroupByMerchId(merchId) {
  return request({
    url: '/api/admin/xzp/listGroupByMerchId',
    method: 'get',
    params: { merchId }
  })
}

// 获取业务代码下拉数据
export function getOpeCdList(query) {
  return request({
    url: '/api/admin/xzp/getOpeCdList',
    method: 'get',
    params: query
  })
}

// 获取委托单位代码下拉数据
export function getMerchIdList(query) {
  return request({
    url: '/api/admin/xzp/getMerchIdList',
    method: 'get',
    params: query
  })
} 