import request from '@/utils/request';

// 新市民查询 - 列表查询;
export function list(data) {
    let url = `/api/admin/xsm/352/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新市民查询 - 列表查询;
export function dataInfoList(data) {
    let url = `/api/admin/xsm/352/dataInfoList?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新市民查询-新增待授权数据
export function addQueryInfo(data) {
    return request({
        url: '/api/admin/xsm/352/addQueryInfo',
        method: 'post',
        data: data,
    });
}

// 新市民查询-批量确认授权查询
export function batchAuthorization(data) {
    return request({
        url: `/api/admin/xsm/352/sureAuthorization`,
        method: 'post',
        data: data,
    });
}

//导出新市民数据接口
export function exportXsm(params) {
    return request({
        url: '/api/admin/xsm/352/actions/export',
        method: 'post',
        data: params,
        responseType: 'blob',
    });
}

//下载新市民导入模板
export function downloadExample(data) {
    return request({
        url: '/api/admin/xsm/352/template/actions/download',
        method: 'get',
        data: data,
        responseType: 'blob',
    });
}

//批量导入数据接口
export function importXsm(data) {
    return request({
        url: '/api/admin/xsm/352/actions/import',
        method: 'post',
        data: data,
        contentType: false,
        processData: false,
        headers: {
            'Content-Type': 'multipart/form-data;',
        },
    });
}

// 新市民查询-查询当前登录人信息
export function getCurrentUserInfo() {
    return request({
        url: '/api/admin/xsm/352/getCurrentUserInfo',
        method: 'get',
    });
}

//查询数据敏感信息(白鹭分)
export function dataSensitive(id, sensitiveStatus) {
    return request({
        url: `/api/admin/xsm/352/sensitive/${id}/${sensitiveStatus}`,
        method: 'get',
    });
}

//查询数据敏感信息（查询日志）
export function infoSensitive(id, sensitiveStatus) {
    return request({
        url: `/api/admin/xsm/352/infoSensitive/${id}/${sensitiveStatus}`,
        method: 'get',
    });
}
