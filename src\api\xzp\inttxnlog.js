import request from '@/utils/request'

// 查询交易流水列表
export function listIntTxnLog(query) {
  return request({
    url: '/api/xzp/intTxnLog/intTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询交易流水列表（关联查询）
export function selectIntTxnLogList(query) {
  return request({
    url: '/api/xzp/intTxnLog/selectIntTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询缴费明细
export function queryTxnLogDetail(query) {
  return request({
    url: '/api/xzp/intTxnLog/queryTxnLogDetail',
    method: 'post',
    data: query
  })
}