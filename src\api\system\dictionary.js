import request from '@/utils/request'

/**
 * 2022/3/2
 * @param {* dictTypeId:字典类型,dictTypeName:字典名称,dataSource:数据来源,status:状态,pageNum：当前页数,pageSize：每页几条} query 
 */
export function dictList (data) {
  const { dictTypeId = "", dictTypeName = "",dataSource="", status = "", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/dict/types/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      dictTypeId,
      dictTypeName,
      dataSource,
      status
    }
  })
}

// 查询角色详细
export function getRole (roleId) {
  return request({
    url: '/system/role/' + roleId,
    method: 'get'
  })
}

// 新增字典
export function addDict (data) {
  return request({
    url: '/api/dict/types/actions/add',
    method: 'post',
    data: data
  })
}

//批量删除字典
export function delMoreDict (data) {
  return request({
    url: '/api/dict/types/actions/remove',
    method: 'post',
    data: data
  })
}

//下载用户信息导入模板
export function downloadExample(data) {
  return request({
    url: '/api/dict/types/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob'
  })
}

//导入字典
export function importDict (data) {
  return request({
    url: '/api/dict/types/actions/import',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

//导出接口
export function exportDict(data) {
  return request({
    url: '/api/dict/types/actions/export',
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: 20000
  })
}

//删除字典
export function delDict (dictTypeId) {
  return request({
    url: `/api/dict/types/actions/remove/${dictTypeId}`,
    method: 'post'
  })
}

//修改字典
export function updateDict (data) {
  return request({
    url: '/api/dict/types/actions/edit',
    method: 'post',
    data: data
  })
}

//字典项列表
export function entriesList (data) {
  const { dictTypeId = "", dictId = "",dictName="", status = "", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/dict/entries/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      dictTypeId,
      dictId,
      dictName,
      status
    }
  })
}

//新增字典项
export function addEntries (data) {
  const { dictTypeId = "", dictId = "",dictName="", status = "", displayOrder=""} = data || {}
  return request({
    url: '/api/dict/entries/actions/add',
    method: 'post',
    data: {
      dictTypeId,
      dictId,
      dictName,
      status,
      displayOrder
    }
  })
}

//批量删除字典项
export function delMoreEntries (data) {
  return request({
    url: '/api/dict/entries/actions/remove',
    method: 'post',
    data: data
  })
}

//删除字典项
export function delEntries (dictTypeId,dictId) {
  return request({
    url: `/api/dict/entries/actions/remove/${dictTypeId}/${dictId}`,
    method: 'post'
  })
}

//修改字典项
export function updateEntries (data) {
  return request({
    url: '/api/dict/entries/actions/edit',
    method: 'post',
    data: data
  })
}