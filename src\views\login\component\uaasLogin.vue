<template>
  <!-- 统一认证登录 -->
  <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
    <el-form-item prop="username">
      <el-input v-model.trim="loginForm.username" type="text" auto-complete="off" placeholder="用户名">
        <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
      </el-input>
    </el-form-item>
    <el-form-item prop="password" v-if="pwdOnOff">
      <el-input
        v-model.trim="loginForm.password"
        type="password"
        auto-complete="off"
        placeholder="密码"
        @keyup.enter.native="handleLogin"
        show-password
      >
        <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
      </el-input>
    </el-form-item>
    <el-form-item prop="captcha">
      <el-input
        v-model="loginForm.captcha"
        auto-complete="off"
        placeholder="验证码"
        style="width: 63%"
        @keyup.enter.native="handleLogin"
      >
        <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
      </el-input>
      <div class="login-code">
        <el-button
          v-if="authNType=='01'"
          style="width:100%;color:#666;height:38px;"
          :disabled="sendDisabled"
          @click="onSend"
        >{{text}}</el-button>
        <img v-if="authNType=='02'" :src="codeUrl" @click="onSend" class="login-code-img" />
      </div>
    </el-form-item>
    <el-form-item style="width:100%;">
      <el-button
        :loading="loading"
        size="medium"
        type="primary"
        style="width:100%;"
        @click.native.prevent="handleLogin"
      >
        <span v-if="!loading">登 录</span>
        <span v-else>登 录 中...</span>
      </el-button>
    </el-form-item>
    <el-form-item style="width:100%;">
      <el-link :href="portalurl" :underline="false" target="_blank">统一认证链接</el-link>
    </el-form-item>
  </el-form>
</template>

<script>
import CryptoJS from '@/assets/lib/crypto-js.js';
import {sm2} from 'sm-crypto';
import { constant } from "@/utils/constant";
const INIT_TEXT = "获取验证码";
const TIP_TEXT = "{{time}}s后重获取";
import {
  getCodeImg,
  getCodeSms,
  uaasPortalurl,
  getuaasLogin
} from "@/api/login";
import { encrypt, UIAEncrypt, base64Str } from "@/utils/smencrypt.js";
import local from "@/plugins/cache";

export default {
  props: {
    // 密码显示开关
    pwdOnOff: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  data() {
    return {
      portalurl: "",
      codeUrl: "",
      authNType: "01",
      loginForm: {
        username: "",
        password: "",
        captcha: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        captcha: [{ required: true, trigger: "blur", message: "请输入验证码" }]
      },
      loading: false,
      redirect: "",
      check: null,
      text: "",
      nowtime: "",
      countDownTime: 60,
      userStartStr: '{dhjdfu34i34u34-zmew8732dfhjd-',
      userEndStr: 'dfhjdf8347sdhxcye-ehjcbeww34}',
    };
  },
  computed: {
    sendDisabled() {
      return !this.validatenull(this.check);
    }
  },
  created() {
    uaasPortalurl().then(res => {
      if (res.code == 0) {
        // this.portalurl = `${res.message}?redirect=${window.location.origin}/login`;
        this.portalurl = res.message;
      }
    });
    let sendEndTime = local.local.get("startTimePhonePwd");
    if (sendEndTime) {
      this.countDown();
    } else {
      this.text = INIT_TEXT;
    }
  },
  methods: {
    onSend() {
      if (!this.loginForm.username) return;
      let newCodeStr = this.loginForm.password + this.userStartStr + this.loginForm.username + this.userEndStr;
      const name = {
        userLoginName: this.loginForm.username,
        // userLoginPwd: encrypt(this.loginForm.password)
        userLoginPwd: base64Str(UIAEncrypt(this.loginForm.password,this.loginForm.username))
      };
      getuaasLogin(name).then(res => {
        const resCode = res.code;
        if (resCode === '0') {
          this.authNType = res.data.authNType;
          this.codeUrl = res.data.authNContent;
          if (this.sendDisabled || this.authNType == "02") return;
          this.$message.success(res.message);
          this.countDown();
        } else {
          this.$message.error(res.message || '验证码发送异常');
        }
      });
    },
    countDown() {
      let startTime = local.local.get("startTimePhonePwd");
      let nowTime = new Date().getTime();
      if (startTime) {
        let surplus =
          this.countDownTime - parseInt((nowTime - startTime) / 1000, 10);
        this.countDownTime = surplus <= 0 ? 0 : surplus;
      } else {
        this.countDownTime = 60;
        local.local.set("startTimePhonePwd", nowTime);
      }
      this.check = setInterval(() => {
        this.countDownTime--;
        // this.text = TIP_TEXT.replace("{{time}}", this.countDownTime);
        if (this.countDownTime <= 0) {
          local.local.remove("startTimePhonePwd");
          this.text = INIT_TEXT;
          clearInterval(this.check);
          this.check = null;
          this.countDownTime = 60;
        } else {
          this.text = TIP_TEXT.replace("{{time}}", this.countDownTime);
        }
      }, 1000);
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          const { username, password, captcha } = this.loginForm;
          let newCodeStr = password + this.userStartStr + username + this.userEndStr;
          const submitData = {
            username,
            // password: encrypt(password),
            password: base64Str(UIAEncrypt(password,username)),
            captcha
          };
          this.dynamicToken = false;
          this.$store
            .dispatch("Login", submitData)
            .then(() => {
              this.loading = false;
              // 开启不输入手机号动态验证时
              if (this.dynamicToken) {
                this.$prompt("请输入动态验证码", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  closeOnClickModal: false
                })
                  .then(({ value }) => {
                    this.$router
                      .push({ path: this.redirect || "/" })
                      .catch(() => {});
                  })
                  .catch(() => {
                    this.loading = false;
                    this.dynamicToken = false;
                  });
                this.dynamicToken = false;
              } else {
                this.$router
                  .push({ path: this.redirect || "/" })
                  .catch(() => {});
              }
            })
            .catch(() => {
              this.loading = false;
              // local.local.remove("startTimePhonePwd");
              // this.text = INIT_TEXT;
              // clearInterval(this.check);
              // this.check = null;
              // this.countDownTime = 60;
              this.getCode();
            });
        }
      });
    }
  }
};
</script>
<style  scoped>
.el-link {
  color: rgb(219, 223, 226);
  display: block;
  width: 50%;
  text-align: center;
  margin: 0 auto;
}
.el-link:hover {
  color: #fff;
}
</style>