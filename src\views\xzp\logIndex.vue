<!--新中平操作日志 -->
<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @refresh-change="refresh" v-model="formParent" @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>
      <!-- 自定义搜索 -->
      <template slot-scope="{ disabled, size }" slot="createTimeSearch">
        <el-date-picker clearable v-model="search.createTimeBox" type="datetimerange" range-separator="至"
          start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']" @change="changeFn()"></el-date-picker>
      </template>


      <!-- 自定义右侧操作栏 -->
      <template slot="menuRight">

        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>

      </template>

      <!-- 自定义显示隐藏列 -->
      <template slot="createBy" slot-scope="scope">
        <div>
          <span>{{scope.row.createBy}}</span>
          <el-button v-if="scope.row.createBy" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'createBy')"></el-button>
        </div>
      </template>

      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          查看
        </el-button>
      </template>
    </yo-table>

  </div>
</template>
<script>
import {
  xzpLogList,
  exportLogs,
  viewLog,
  querySensitive
} from '@/api/xzplog';
import logOption from './infoData/xzpOpLog.js';
import Cookies from "js-cookie";

export default {
  name: 'xzpLog',
  components: {},
  data () {
    return {
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: logOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    "search.createTimeBox": {
      handler (val) {
        if (val) {
          this.search.createTimeStart = val[0];
          this.search.createTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    changeFn () {
      if (!this.search.createTimeBox) {
        this.search.createTimeStart = ''
        this.search.createTimeEnd = ''
      }
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await xzpLogList(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.id).join(',');
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },



    /** 导出结果，处理返回的流文件 */
    handleExportData (res) {
      console.log('res:', res);
      if (!res) return;
      let data = res.data;
      let filename = res.headers['content-disposition'].split('=')[1];
      let _filename = decodeURI(filename);
      const link = document.createElement('a');
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' });
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 导出按钮操作 */
    handleExport () {
      const data = { userIds: this.ids, ...this.exportSearch };
      // console.log('导出数据：', this.search);
      exportLogs(this.search).then(res => {
        // console.log('导出的返回数据：', res);
        this.handleExportData(res);
      });
    },


    //查看
    async handleView (row) {
      var id = row.id;
      viewLog(id).then(res => {
        console.log("查询返回的结果--" + JSON.stringify(res))
        if (res.code == 0) {
          this.$refs.crud.rowView(res.data);
        }
      });
    },


  /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.id, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("新中平外联操作日志敏感信息响应结果：" + res);
        this.data.forEach(item => {
          if (item.id == data.id) {
            item[type] = res[type];
          }
        });
      });
    },

    //end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}
</style>
