import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

//新中平接口相关

// 新中平-监管账户信息变动反馈情况查询 - 列表查询;
export function list (data) {
  let url = `/api/admin/JgzhList?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}

// 新中平-监管账户信息变动反馈-新增数据
export function addJgzhInfo (data) {
  const {
    id = "",
      stockNum = "",
      impTime = "",
      othType = "",
      othWarrantNum = "",
      othRecordDate = "",
      borrowName = "",
      borrowIdCard = "",
      handleOrg = "",
      handleCustManager = "",
      loanType = "",
      loanContractNum = "",
      loanLife = "",
      recordedValue = "",
      packetNum = "",
      successor = "",
      purchaseContractNum = "",
      noteInfo = "",
      stockStatus = "",
      suppRegistMark = "",
      pledgeStatusAfterlogout = "",
      propertycard = "",
      propertyownerName = "",
      propertyownerIdCard = "",
      propertyrightAddr = "",
      propertycardNum = "",
      propertycardIssueDate = "",
      undetermined = ""
  } = data || {}
  return request({
    url: '/api/admin/xzp/addJgzhInfo',
    method: 'post',
    data: {
      id,
      stockNum,
      impTime,
      othType,
      othWarrantNum,
      othRecordDate,
      borrowName,
      borrowIdCard,
      handleOrg,
      handleCustManager,
      loanType,
      loanContractNum,
      loanLife,
      recordedValue,
      packetNum,
      successor,
      purchaseContractNum,
      noteInfo,
      stockStatus,
      suppRegistMark,
      pledgeStatusAfterlogout,
      propertycard,
      propertyownerName,
      propertyownerIdCard,
      propertyrightAddr,
      propertycardNum,
      propertycardIssueDate,
      undetermined
    }
  });
}

// 新中平-监管账户信息变动反馈-修改
export function editJgzh (data) {
  const {
     id = "",
      stockStatus = data.$stockStatus,
      suppRegistMark = data.$suppRegistMark,
      pledgeStatusAfterlogout = data.$pledgeStatusAfterlogout,
      noteInfo = data.$noteInfo,
      propertycardNum = data.$propertycardNum,
      undetermined = data.$undetermined
  } = data || {}
  return request({
    url: `/api/admin/xXJgzhtJgzh`,
    method: 'post',
    data: {
      id,
      stockStatus,
      suppRegistMark,
      pledgeStatusAfterlogout,
      noteInfo,
      propertycardNum,
      undetermined
    }
  });
}

// 新中平-监管账户信息变动反馈-详情
export function viewJgzh (id) {
  return request({
    url: '/api/admin/JgzhDetail/' + parseStrEmpty(id),
    method: 'get'
  })

}

// 监管账户-批量修改状态
export function batchUpdateJgzh (data) {
  return request({
    url: `/api/admin/jgzhBatch`,
    method: 'post',
    data: data,
  });
}

//导出监管账户数据接口
export function exportJgzh (params) {
  return request({
    url: '/api/admin/xzp/JgzhExport',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

//下载新中平-监管账户信息变动反馈导入模板
export function downloadExample (data) {
  return request({
    url: '/api/admin/xzp/Jgzh/download',
    method: 'get',
    data: data,
    responseType: 'blob',
  });
}

//批量导入数据接口
export function importJgzh (data) {
  return request({
    url: '/api/admin/xzp/actions/impJgzh',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}
//增量导入数据接口
export function importJgzhAdd (data) {
  return request({
    url: '/api/admin/xzp/action/impJgzhAdd',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}

// 删除用户
export function delJgzh (id) {
  return request({
    url: '/api/admin/xzp/removeJgzh/' ,
    method: 'post',
  })
}

// 批量删除用户
export function delJgzhs (ids) {
  return request({
    url: '/api/admin/xzp/removeJgzhInfos',
    method: 'post',
    data: ids
  })
}
