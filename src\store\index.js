import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import userInfo from './modules/userInfo'
import instInfo from './modules/inst'
import dictInfo from './modules/dict'
Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    userInfo,
    instInfo,
    dictInfo,
  },
  getters
})

export default store
