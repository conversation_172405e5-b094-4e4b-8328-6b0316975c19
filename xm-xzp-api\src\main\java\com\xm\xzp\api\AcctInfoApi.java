package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 账户信息查询API
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "账户信息数据源列表")
@Validated
public interface AcctInfoApi {

    /**
     * 根据户名或公司账户模糊查询账户信息列表（用于联想下拉控件）
     *
     * @param keyword 关键字，可以是户名(acct_nm)或公司账户(cpab_acc_id)，为空时返回全量数据
     * @return 账户信息列表
     */
    @ApiOperation(value = "账户信息联想查询", notes = "suggestAcctInfo")
    @GetMapping("/suggestAcctInfo")
    RestResponse<List<TbZjjgAcctInfo>> suggestAcctInfo(
            @ApiParam(value = "查询关键字（户名或公司账户），为空时返回全量数据")
            @RequestParam(value = "keyword", required = false) String keyword
    );

    /**
     * 分页查询账户信息列表
     *
     * @param queryVo 查询条件
     * @param pageNum 当前页
     * @param pageSize 每页数量
     * @return 分页账户信息列表
     */
    @ApiOperation(value = "账户信息分页查询", notes = "queryAcctInfoList")
    @PostMapping("/queryAcctInfoList")
    RestResponse<PageInfo<TbZjjgAcctInfo>> queryAcctInfoList(
            @RequestBody AcctInfoQueryVo queryVo,
            @ApiParam(value = "当前页", defaultValue = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页数量", defaultValue = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    );

    /**
     * 新增账户信息
     *
     * @param acctInfo 账户信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增账户信息", notes = "addAcctInfo")
    @PostMapping("/addAcctInfo")
    RestResponse<String> addAcctInfo(
            @ApiParam(value = "账户信息")
            @RequestBody @Valid TbZjjgAcctInfo acctInfo
    );

    /**
     * 修改账户信息
     *
     * @param acctInfo 账户信息
     * @return 操作结果
     */
    @ApiOperation(value = "修改账户信息", notes = "updateAcctInfo")
    @PostMapping("/updateAcctInfo")
    RestResponse<String> updateAcctInfo(
            @ApiParam(value = "账户信息")
            @RequestBody @Valid TbZjjgAcctInfo acctInfo
    );

    /**
     * 删除账户信息
     *
     * @param cpabAccId 公司账户
     * @param acctNm 户名
     * @return 操作结果
     */
    @ApiOperation(value = "删除账户信息", notes = "deleteAcctInfo")
    @PostMapping("/deleteAcctInfo")
    RestResponse<String> deleteAcctInfo(
            @ApiParam(value = "公司账户", required = true)
            @RequestParam("cpabAccId") String cpabAccId,
            @ApiParam(value = "户名", required = true)
            @RequestParam("acctNm") String acctNm
    );

    /**
     * 获取账户敏感信息（脱敏/不脱敏）
     *
     * @param cpabAccId 公司账户
     * @param acctNm 户名
     * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
     * @return 账户信息
     */
    @ApiOperation(value = "获取账户敏感信息", notes = "getAcctSensitiveInfo")
    @GetMapping("/acctSensitive/{cpabAccId}/{acctNm}/{sensitiveStatus}")
    RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfo(
            @ApiParam(value = "公司账户", required = true)
            @PathVariable("cpabAccId") String cpabAccId,
            @ApiParam(value = "户名", required = true)
            @PathVariable("acctNm") String acctNm,
            @ApiParam(value = "敏感状态：1-脱敏，2-不脱敏", required = true)
            @PathVariable("sensitiveStatus") String sensitiveStatus
    );

    /**
     * 根据业务标识获取账户敏感信息（脱敏/不脱敏）- 使用oth_msg2_tx作为唯一标识
     *
     * @param othMsg2Tx 唯一标识
     * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
     * @return 账户信息
     */
    @ApiOperation(value = "根据唯一标识获取账户敏感信息", notes = "getAcctSensitiveInfoByBizKey")
    @GetMapping("/acctSensitiveByBizKey/{othMsg2Tx}/{sensitiveStatus}")
    RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(
            @ApiParam(value = "唯一标识", required = true)
            @PathVariable("othMsg2Tx") String othMsg2Tx,
            @ApiParam(value = "敏感状态：1-脱敏，2-不脱敏", required = true)
            @PathVariable("sensitiveStatus") String sensitiveStatus
    );

    /**
     * 根据oth_msg2_tx获取账户详情
     *
     * @param othMsg2Tx 唯一标识
     * @return 账户信息
     */
    @ApiOperation(value = "根据唯一标识获取账户详情", notes = "getAcctInfoByOthMsg2Tx")
    @GetMapping("/getAcctInfoByOthMsg2Tx/{othMsg2Tx}")
    RestResponse<TbZjjgAcctInfo> getAcctInfoByOthMsg2Tx(
            @ApiParam(value = "唯一标识", required = true)
            @PathVariable("othMsg2Tx") String othMsg2Tx
    );

    /**
     * 根据oth_msg2_tx修改账户信息
     *
     * @param acctInfo 账户信息（必须包含othMsg2Tx字段）
     * @return 操作结果
     */
    @ApiOperation(value = "根据唯一标识修改账户信息", notes = "updateAcctInfoByOthMsg2Tx")
    @PostMapping("/updateAcctInfoByOthMsg2Tx")
    RestResponse<String> updateAcctInfoByOthMsg2Tx(
            @ApiParam(value = "账户信息（必须包含othMsg2Tx字段）")
            @RequestBody @Valid TbZjjgAcctInfo acctInfo
    );

    /**
     * 根据oth_msg2_tx删除账户信息
     *
     * @param othMsg2Tx 唯一标识
     * @return 操作结果
     */
    @ApiOperation(value = "根据唯一标识删除账户信息", notes = "deleteAcctInfoByOthMsg2Tx")
    @PostMapping("/deleteAcctInfoByOthMsg2Tx")
    RestResponse<String> deleteAcctInfoByOthMsg2Tx(
            @ApiParam(value = "唯一标识", required = true)
            @RequestParam("othMsg2Tx") String othMsg2Tx
    );
}