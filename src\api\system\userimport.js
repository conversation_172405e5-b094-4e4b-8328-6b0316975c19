import request from '@/utils/request'

/**
 * 2024/12/9
 * @param {*userCode: 用戶编号,userName: 用戶名,requestURI:请求地址,status:状态,pageNum：当前页数,pageSize：每页几条} data
 */
// 分页查询导入用户列表
export function listConfig(data) {
  const {year = '', yearQuar = '', yearMonth = '', userCode = '', userName = '',requestURI = '',status = '',  currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/jxkh/userImport/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {year,yearQuar,yearMonth,userCode, userName, requestURI, status}
  })
}


//导入人员信息接口
export function importConfig(data) {
  return request({
    url: '/api/admin/jxkh/userImport/actions/import',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

//查询用户敏感信息
export function importSensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/jxkh/import/sensitive/actions/get/${id}/${sensitiveStatus}`,
    method: 'get',
  })
}

//下载 导入模板
export function downloadExample(data) {
  return request({
    url: "/api/admin/jxkh/import/template/actions/download",
    method: "get",
    data: data,
    responseType: "blob"
  });
}

// 新增人员
export function addUserImport(data) {
  return request({
    url: "/api/admin/jxkh/userImport/actions/add",
    method: "post",
    data: data
  });
}
//从工资管理页面新增人员

export function addUserJx(fromData) {
  return request({
    url: "/api/admin/jxkh/userImport/actions/inuser",
    method: "post",
    data: fromData,
  });
}

// 修改人员
export function updateUserImport(data) {
  return request({
    url: "/api/admin/jxkh/userImport/actions/edit",
    method: "post",
    data: data
  });
}

// 删除岗位
export function delUserImport(id) {
  return request({
    url: "/api/admin/jxkh/userImport/actions/remove/" + id,
    method: "post"
  });
}


// 根据id获取数据
export function getById(id) {
  return request({
    url: "/api/admin/jxkh/userImport/actions/getById/" + id,
    method: "post"
  });
}

// 发起
export function HandleSend(data) {
  const {year = '', yearQuar = '', yearMonth = ''} = data || {}
  return request({
    url: "/api/admin/jxkh/userImport/actions/send",
    method: 'post',
    data: {year,yearQuar,yearMonth}
  })
}
