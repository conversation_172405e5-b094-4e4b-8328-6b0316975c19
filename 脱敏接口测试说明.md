# 脱敏接口测试说明

## 问题分析

用户反馈：页面上有弹窗提醒"已显示明文"，但表格依然是密文形式。

## 可能原因

1. **后端逻辑错误**：敏感状态判断逻辑可能有问题
2. **前端更新失败**：Vue响应式更新可能没有生效
3. **接口调用问题**：参数传递或返回数据有问题

## 修复内容

### 1. 后端逻辑修复

**问题**：原来的判断逻辑是错误的
```java
// 错误的逻辑
if (SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
    // 这里应该是不脱敏，但却执行了脱敏操作
}
```

**修复后**：
```java
// 正确的逻辑
if (SensitiveStatusEnum.SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
    // sensitiveStatus = "1" 表示要脱敏
    // 执行脱敏操作
} else {
    // sensitiveStatus = "2" 表示不脱敏
    // 返回原始数据
}
```

### 2. 前端更新优化

**问题**：Vue响应式更新可能不生效

**修复**：
```javascript
// 多种方式确保更新生效
row.cpabAccId = sensitiveData.cpabAccId;
row.acctNm = sensitiveData.acctNm;
row.sensitiveStatus = sensitiveData.sensitiveStatus;

this.$set(row, 'cpabAccId', sensitiveData.cpabAccId);
this.$set(row, 'acctNm', sensitiveData.acctNm);
this.$set(row, 'sensitiveStatus', sensitiveData.sensitiveStatus);

this.$forceUpdate(); // 强制重新渲染
```

### 3. 详细日志

添加了详细的日志输出，便于调试：
```java
log.info("处理敏感状态，请求状态：{}，脱敏状态码：{}，不脱敏状态码：{}", 
        sensitiveStatus, SensitiveStatusEnum.SENSITIVE_STATUS.statusCode(), 
        SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
```

## 测试步骤

### 1. 后端接口测试

使用Postman或curl测试接口：

**测试脱敏接口**：
```
GET /api/admin/xzp/acctSensitiveByBizKey/{tranTime}/{opeCd}/{merchId}/1
```
期望：返回脱敏数据

**测试明文接口**：
```
GET /api/admin/xzp/acctSensitiveByBizKey/{tranTime}/{opeCd}/{merchId}/2
```
期望：返回明文数据

### 2. 前端功能测试

1. **页面加载**：确认数据默认是脱敏状态
2. **点击显隐按钮**：
   - 第一次点击：应该显示明文
   - 第二次点击：应该重新脱敏
3. **检查控制台**：查看日志输出

### 3. 调试方法

#### 后端调试
1. 查看服务器日志，确认：
   - 接口是否被正确调用
   - 参数是否正确传递
   - 返回数据是否正确

#### 前端调试
1. 打开浏览器开发者工具
2. 查看Network标签，确认：
   - API请求是否发送
   - 返回数据是否正确
3. 查看Console标签，确认：
   - 是否有JavaScript错误
   - 日志输出是否正常

## 状态码说明

- **"1"** - 脱敏状态（SENSITIVE_STATUS）
- **"2"** - 不脱敏状态（UN_SENSITIVE_STATUS）

## 预期行为

1. **默认状态**：页面加载时，数据应该是脱敏状态（sensitiveStatus = "1"）
2. **第一次点击**：
   - 前端发送请求：`sensitiveStatus = "2"`（要求明文）
   - 后端返回：明文数据 + `sensitiveStatus = "2"`
   - 前端显示：明文数据
3. **第二次点击**：
   - 前端发送请求：`sensitiveStatus = "1"`（要求脱敏）
   - 后端返回：脱敏数据 + `sensitiveStatus = "1"`
   - 前端显示：脱敏数据

## 常见问题排查

### 1. 接口返回数据仍然是脱敏的
- 检查后端日志，确认判断逻辑是否正确
- 确认传入的sensitiveStatus参数值

### 2. 前端表格没有更新
- 检查Vue响应式更新是否生效
- 尝试使用`$forceUpdate()`强制更新
- 检查是否有JavaScript错误

### 3. 参数传递问题
- 确认业务标识字段（tranTime、opeCd、merchId）是否完整
- 检查URL编码是否正确

## 示例测试数据

假设有一条记录：
- **原始数据**：
  - cpabAccId: "123456789012"
  - acctNm: "张三公司"
  - tranTime: "20241201120000"
  - opeCd: "001"
  - merchId: "M001"

- **脱敏数据**：
  - cpabAccId: "123*****9012"
  - acctNm: "张***"

**测试URL**：
```
GET /api/admin/xzp/acctSensitiveByBizKey/20241201120000/001/M001/2
```

**期望返回**：
```json
{
  "code": "0",
  "data": {
    "cpabAccId": "123456789012",
    "acctNm": "张三公司",
    "sensitiveStatus": "2",
    // ... 其他字段
  }
}
```
