# 脱敏功能后端实现说明

## 修改概述

将监管账户和监管账户名的脱敏实现从前端移到后端，模仿`JgzhSpDataServiceImpl.java`的脱敏实现方式。

## 修改内容

### 1. 实体类修改

**文件**: `xm-xzp-api/src/main/java/com/xm/xzp/model/entity/TbZjjgAcctInfo.java`

添加敏感状态字段：
```java
@ApiModelProperty(value = "敏感信息状态：1-脱敏，2-不脱敏")
@TableField(exist = false)
private String sensitiveStatus;
```

### 2. Service接口修改

**文件**: `xm-xzp-impl/src/main/java/com/xm/xzp/service/ITbZjjgAcctInfoService.java`

添加获取敏感信息的方法：
```java
/**
 * 获取账户敏感信息（脱敏/不脱敏）
 * @param cpabAccId 公司账户
 * @param acctNm 户名
 * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
 * @return 账户信息
 */
TbZjjgAcctInfo getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus);
```

### 3. Service实现修改

**文件**: `xm-xzp-impl/src/main/java/com/xm/xzp/service/impl/TbZjjgAcctInfoServiceImpl.java`

#### 3.1 导入依赖
```java
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.util.AdminConstants;
import org.springframework.beans.BeanUtils;
```

#### 3.2 修改分页查询方法
在`queryAcctInfoPage`方法中添加默认脱敏处理：
```java
// 默认对敏感信息进行脱敏处理
for (TbZjjgAcctInfo info : list) {
    // 设置默认脱敏状态
    info.setSensitiveStatus("1"); // 1-脱敏
    
    // 对监管账户进行脱敏
    String cpabAccId = info.getCpabAccId();
    if (StringUtils.hasText(cpabAccId)) {
        cpabAccId = cpabAccId.replaceAll("(?<=.{3}).(?=.{4})", "*");
        info.setCpabAccId(cpabAccId);
    }
    
    // 对账户名进行脱敏
    String acctNm = info.getAcctNm();
    if (StringUtils.hasText(acctNm)) {
        acctNm = acctNm.replaceAll("(?<=.{1}).", "*");
        info.setAcctNm(acctNm);
    }
}
```

#### 3.3 实现获取敏感信息方法
```java
@Override
public TbZjjgAcctInfo getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus) {
    // 查询原始数据
    TbZjjgAcctInfo originalInfo = this.lambdaQuery()
            .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
            .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
            .one();

    // 组装返回信息
    TbZjjgAcctInfo sensitiveInfoVO = new TbZjjgAcctInfo();
    BeanUtils.copyProperties(originalInfo, sensitiveInfoVO);

    // 根据敏感状态进行处理
    if (SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
        // 需要脱敏
        sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());
        // 脱敏处理逻辑...
    } else {
        // 不脱敏
        sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
    }

    return sensitiveInfoVO;
}
```

### 4. API接口修改

**文件**: `xm-xzp-api/src/main/java/com/xm/xzp/api/AcctInfoApi.java`

添加获取敏感信息的接口：
```java
/**
 * 获取账户敏感信息（脱敏/不脱敏）
 */
@ApiOperation(value = "获取账户敏感信息", notes = "getAcctSensitiveInfo")
@GetMapping("/acctSensitive/{cpabAccId}/{acctNm}/{sensitiveStatus}")
RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfo(
        @PathVariable("cpabAccId") String cpabAccId,
        @PathVariable("acctNm") String acctNm,
        @PathVariable("sensitiveStatus") String sensitiveStatus
);
```

### 5. Controller修改

**文件**: `xm-xzp-impl/src/main/java/com/xm/xzp/controller/AcctInfoController.java`

实现获取敏感信息的接口：
```java
@Override
public RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus) {
    TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctSensitiveInfo(cpabAccId, acctNm, sensitiveStatus);
    if (result != null) {
        return RestResponse.success(result);
    } else {
        return RestResponse.fail("获取账户敏感信息失败，可能是记录不存在");
    }
}
```

### 6. 前端API修改

**文件**: `src/api/acctmgmt.js`

添加获取敏感信息的API调用：
```javascript
// 获取账户敏感信息（脱敏/不脱敏）
export function getAcctSensitiveInfo(cpabAccId, acctNm, sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/acctSensitive/${encodeURIComponent(cpabAccId)}/${encodeURIComponent(acctNm)}/${sensitiveStatus}`,
    method: 'get'
  });
}
```

### 7. 前端页面修改

**文件**: `src/views/xzp/acctmgmt/index.vue`

#### 7.1 移除前端脱敏逻辑
- 移除`maskSensitiveData`方法
- 移除数据加载后的脱敏处理
- 后端已默认返回脱敏数据

#### 7.2 修改敏感信息切换方法
```javascript
// 敏感信息显隐切换
async viewSensitiveInfo(row, type) {
  try {
    const currentStatus = row.sensitiveStatus || '1'; // 默认为脱敏状态
    const targetStatus = currentStatus === '1' ? '2' : '1'; // 切换状态
    
    // 调用后端接口获取对应状态的数据
    const response = await getAcctSensitiveInfo(row.cpabAccId, row.acctNm, targetStatus);
    
    if (response && response.data) {
      const sensitiveData = response.data;
      // 更新当前行的敏感字段
      this.$set(row, 'cpabAccId', sensitiveData.cpabAccId);
      this.$set(row, 'acctNm', sensitiveData.acctNm);
      this.$set(row, 'sensitiveStatus', sensitiveData.sensitiveStatus);
      
      this.$message.success(targetStatus === '1' ? '已脱敏' : '已显示明文');
    }
  } catch (error) {
    this.$message.error('操作失败，请重试');
  }
}
```

## 脱敏规则

### 监管账户（cpabAccId）
- **规则**: 保留前3位和后4位，中间用*替换
- **正则**: `(?<=.{3}).(?=.{4})`
- **示例**: `123456789012` → `123*****9012`

### 账户名（acctNm）
- **规则**: 保留第1位，其余用*替换
- **正则**: `(?<=.{1}).`
- **示例**: `张三公司` → `张***`

## 敏感状态说明

- **状态值**: 
  - `1` - 脱敏状态
  - `2` - 不脱敏状态（明文）

- **默认行为**: 
  - 列表查询默认返回脱敏数据
  - 点击显隐按钮可切换状态

## 优势

1. **安全性提升**: 敏感数据处理在后端进行，前端不接触原始数据
2. **统一管理**: 脱敏规则集中在后端，便于维护和修改
3. **性能优化**: 减少前端数据处理逻辑
4. **标准化**: 与项目中其他模块的脱敏实现保持一致

## 测试要点

1. **默认脱敏**: 页面加载时数据应该是脱敏状态
2. **状态切换**: 点击显隐按钮能正确切换脱敏/明文状态
3. **接口调用**: 确认后端接口能正确返回对应状态的数据
4. **错误处理**: 接口调用失败时有适当的错误提示
