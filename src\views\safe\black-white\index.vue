<template>
  <div class="app-container" v-if="showContainer">
    <yo-table
      v-loading="loading"
      :data="data"
      :option="bwOption"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @row-save="rowSave"
      @search-change="searchChange"
      @row-update="rowUpdate"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <template slot="status" slot-scope="scope">
        <el-switch
          v-model="scope.row.switch"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="启用"
          inactive-text="停用"
          @change="changStatus(scope.row)"
          v-hasPermi="['admin:security:black-white:on-off']"
        ></el-switch>
      </template>
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:security:black-white:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
      </template>
      <!-- 自定义左侧操作栏 -->
      <template slot-scope="{size}" slot="menuLeft">
        <el-button
          type="primary"
          icon="el-icon-plus"
          :size="size"
          v-hasPermi="['admin:security:black-white:add']"
          plain
          @click.stop="handleAdd"
        >新增名单</el-button>
        <el-button
          type="danger"
          plain
          v-hasPermi="['admin:security:black-white:remove:batch']"
          icon="el-icon-delete"
          :size="size"
          :disabled="multiple"
          @click="handleDelete"
        >批量删除</el-button>
      </template>
      <template slot-scope="{row,size,type,index}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-edit"
          v-hasPermi="['admin:security:black-white:edit']"
          @click.stop="handleEdit(row,index)"
        >修改</el-button>
        <el-button
          :size="size"
          :type="type"
          v-hasPermi="['admin:security:black-white:remove']"
          icon="el-icon-delete"
          @click="handleDelete(row)"
        >删除</el-button>
      </template>
    </yo-table>
  </div>
</template>
<script>
import {
  list,
  add,
  update,
  del,
  dels,
  updateStatus
} from "@/api/system/safe/blackwhite";
import bwOption from "./infoData/bwOption.js";
export default {
  dicts: ["yoaf_black_white_state", "yoaf_black_white_type"],
  data() {
    return {
      postId: "",
      showContainer: true,
      loading: true,
      formParent: {},
      form: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      bwOption: bwOption,
      multiple: true
    };
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.bwOption.column,
      "status",
      this.dict.type["yoaf_black_white_state"]
    );
    this.updateDictData(
      this.bwOption.column,
      "listType",
      this.dict.type["yoaf_black_white_type"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    showDetails() {
      this.showContainer = true;
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    changStatusII(row) {
      updateStatus(row).then(response => {
        if (response.code == 0) {
          this.getList();
        }
      });
    },
    changStatus(row) {
      let dataCur = {
        id: row.id,
        status: row.switch ? 1 : 2
      };
      updateStatus(dataCur).then(response => {
        if (response.code == 0) {
          this.getList();
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    /** 分页查询列表 */
    async getList(query) {
      const params = { ...query, ...this.page };
      this.loading = true;
      let response = await list(params);
      if (response.code == 0) {
        response.data.list.map(item => {
          if (item.status == "1") {
            item.switch = true;
          } else {
            item.switch = false;
          }
        });
        setTimeout(() => {
          this.data = response.data.list;
          this.page.total = response.data.total;
          this.loading = false;
        }, 1000);
      }
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleBlackWhite":
          this.handleMoreProcess(row, "BlackWhite");
          break;
        case "handleCheckInfo":
          const params = { ...row, ...this.page };
          listJops(params).then(response => {
            this.$refs.crud.rowView(response.data);
          });
          break;
        default:
          break;
      }
    },
    handleBlackWhite(row) {
      this.handleMoreProcess(row, "BlackWhite");
    },
    /** 分配数据权限操作 or  分配用户操作*/
    handleMoreProcess(row, type) {
      this.postId = row.postId;
      this.showContainer = false;
      this.isActive = type;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.id = selection.map(item => item.id);
      this.multiple = !selection.length;
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        default:
          break;
      }
    },
    jopsStat(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        default:
          break;
      }
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    handleEdit(row, index) {
      this.formParent = row;
      this.$refs.crud.rowEdit(row, index);
    },
    // 修改表单保存
    async rowUpdate(form, index, done, loading) {
      let response = await update(form);
      if (response.code == 0) {
        this.$modal.msgSuccess(response.message);
        this.getList();
        done();
      }
    },
    // 新增表单保存
    async rowSave(form, done, loading) {
      let response = await add(form);
      if (response.code == 0) {
        this.$modal.msgSuccess(response.message);
        this.getList();
        done();
      }
    },
    async handleDelJops(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getList();
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.id;
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h("p", { style: "word-break: break-all" }, "是否永久删除此项数据项？")
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(async () => {
          // 删除单个用户
          if (row.id) {
            this.handleDelJops(id, del);
          } else {
            this.handleDelJops(id, dels);
          }
        })
        .catch(() => {});
    }
  }
};
</script>