package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@TableName("tb_zjjg_tran_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SupAcctPayInst对象", description = "监管账户支付指令列表")
public class SupAcctPayInst {
    @ApiModelProperty(value = "委托单位代码")
    private String  merchId;

    @ApiModelProperty(value = "业务代码")
    private String  opeCd;

    @ApiModelProperty(value = "业务范围")
    private String  transType;

    @ApiModelProperty(value = "交易日期")
    private String  tranDt;

    @ApiModelProperty(value = "指令流水号")
    private String  tranSq;

    @ApiModelProperty(value = "开户机构/开户行代")
    private String  openBrhId;

    @ApiModelProperty(value = "开户机构名称/开户网点名称")
    private String  openBrhNm;

    @ApiModelProperty(value = "公司账户")
    private String  cpabAccId;

    @ApiModelProperty(value = "户名")
    private String  acctNm;

    @ApiModelProperty(value = "缴款编号")
    private String  vchNo;

    @ApiModelProperty(value = "交易金额")
    private String  tranAt;

    @ApiModelProperty(value = "发生额")
    private String  hapTranAt;

    @ApiModelProperty(value = "未交金额")
    private String  nohapTranAt;

    @ApiModelProperty(value = "账户余额")
    private String  balance;

    @ApiModelProperty(value = "借贷标志  1 借；2 贷")
    private String  cdFg;

    @ApiModelProperty(value = "公司流水")
    private String  cpcbTranSq;

    @ApiModelProperty(value = "中平流水")
    private String  cpabTranSq;

    @ApiModelProperty(value = "委托方流水")
    private String  merTranSq;

    @ApiModelProperty(value = "支付类型 1：同行、2：跨行")
    private String  vchType;

    @ApiModelProperty(value = "交易状态/支付指令状态   0-待支付，1-已支付，2-退款")
    private String  tranFg;

    @ApiModelProperty(value = "证件类型")
    private String  paperType;

    @ApiModelProperty(value = "证件号码")
    private String  paperId;

    @ApiModelProperty(value = "目标账户开户机构/开户行代码")
    private String  peerBrhId;

    @ApiModelProperty(value = "目标账户开户机构名称/开户网点名称")
    private String  peerBrhNm;

    @ApiModelProperty(value = "目标账户账号")
    private String  peerAccId;

    @ApiModelProperty(value = "目标账户户名")
    private String  peerAccNm;

    @ApiModelProperty(value = "交易码")
    private String  tranCd;

    @ApiModelProperty(value = "附言/备注")
    private String  aptnTx;

    @ApiModelProperty(value = "摘要")
    private String  actxtTx;

    @ApiModelProperty(value = "银行终端编号")
    private String  bkTermId;

    @ApiModelProperty(value = "交易时间（存放支付指令上送的opedate)")
    private String  tranTime;

    @ApiModelProperty(value = "最后交易机构")
    private String  txnBrhId;

    @ApiModelProperty(value = "最后操作员")
    private String  tlrId;

    @ApiModelProperty(value = "复核员")
    private String  checkerId;

    @ApiModelProperty(value = "附加字段1")
    private String  othMsg1Tx;

    @ApiModelProperty(value = "附加字段2")
    private String  othMsg2Tx;

    @ApiModelProperty(value = "附加字段3-pdf文件名（含路径）")
    private String  othMsg3Tx;

    @ApiModelProperty(value = "附加字段4-pdf验证签名状态：有效、无效")
    private String  othMsg4Tx;

    @ApiModelProperty(value = "附加字段5-pdf证书信息")
    private String  othMsg5Tx;

    @ApiModelProperty(value = "最后更新时间时间戳")
    private String  lastUpdateTime;
}
