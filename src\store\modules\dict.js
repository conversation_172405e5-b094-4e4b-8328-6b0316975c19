import { getDicts } from '@/api/system/dict/data'
const dictInfo = {
  state: {
  },

  mutations: {

  },

  actions: {
    // 获取字典信息
    getDicts ({ commit, state }, typeId) {
      return new Promise((resolve, reject) => {
        getDicts(typeId).then(response => {
          const res = response.data
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default dictInfo
