<template>
  <div class="app-container">
    <yo-table
      v-loading="loading"
      :data="data"
      :option="tableOption"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      v-model="formParent"
    >
      <template slot-scope="{ scope }" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['audit:log:page']"
          >查询</el-button
        >
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery"
          >重置</el-button
        >
      </template>
      <!-- 自定义搜索 -->
      <template slot-scope="{ disabled, size }" slot="operateTimeSearch">
        <el-date-picker
          clearable
          v-model="search.operateTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="changeFn()"
        ></el-date-picker>
      </template>
      <!-- 自定义列 -->
      <template slot="status" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{
          status(scope.row)
        }}</el-tag>
      </template>
      <template slot="totalTime" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{scope.row.totalTime}}ms</el-tag>
      </template>
      <!-- 自定义查看内容 -->
      <template slot-scope="scope" slot="executeMethodForm">
        <el-popover
          :open-delay="500"
          placement="top-start"
          trigger="hover"
          :content="formParent.executeMethod"
        >
          <span slot="reference">{{
            formParent.executeMethod | ellipsis
          }}</span>
        </el-popover>
      </template>
      <template slot-scope="scope" slot="contentForm">
        <el-popover
          placement="top-start"
          trigger="hover"
          :open-delay="500"
          :content="formParent.content"
        >
          <span slot="reference">{{ formParent.content | ellipsis }}</span>
        </el-popover>
      </template>
      <!-- 自定义操作栏 -->
      <template slot-scope="{ row, size, type, index }" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-info"
          @click="handleCheckInfo(row)"
          v-hasPermi="['audit:log:get']"
          >查看</el-button
        >
      </template>
    </yo-table>
  </div>
</template>

<script>
import { listRecord, getRecordId } from "@/api/system/auditlog/record";
import tableOption from "./infoData/tableoption.js";
export default {
  name: "Auditlog",
  dicts: [
    "yoaf_system_code",
    "yoaf_request_state",
    "yoaf_request_action",
    "yoaf_request_type",
  ],
  data() {
    return {
      loading: true,
      formParent: {},
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      data: [],
      tableOption: tableOption,
      // 非多个禁用
      multiple: true,
    };
  },
  filters: {
    ellipsis(value) {
      if (!value) return "";
      if (value.length > 90) {
        return value.slice(0, 90) + "......";
      }
      return value;
    },
  },
  watch: {
    "search.operateTime": {
      handler(val) {
        if (val) {
          this.search.operateTimeStart = val[0];
          this.search.operateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.tableOption.column,
      "systemCode",
      this.dict.type["yoaf_system_code"]
    );
    this.updateDictData(
      this.tableOption.column,
      "status",
      this.dict.type["yoaf_request_state"]
    );
    this.updateDictData(
      this.tableOption.column,
      "action",
      this.dict.type["yoaf_request_action"]
    );
    this.updateDictData(
      this.tableOption.column,
      "requestType",
      this.dict.type["yoaf_request_type"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    changeFn() {
      if(!this.search.operateTime) {
        this.search.operateTimeStart = ''
        this.search.operateTimeEnd = ''
      }
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    /** 分页查询审计日志列表 */
    getList(query) {
      const params = { ...query, ...this.page };
      this.loading = true;
      listRecord(params).then((response) => {
        if (response.code == 0) {
          this.data = response.data.list;
          this.page.total = response.data.total;
          this.loading = false;
        } 
      });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
    },

    //查看详情
    handleCheckInfo(row) {
      const id = row.id;
      getRecordId(id).then((response) => {
        response.data.totalTime = response.data.totalTime + 'ms'
        this.$refs.crud.rowView(response.data);
        if (response.code == 0) {
          this.$refs.crud.rowView(response.data);
        } 
      });
    },
    status(row) {
      switch (row.status) {
        case "SUCCESS":
          return "成功";
        case "FAILURE":
          return "失败";
        case "03":
          return "注销";
        case "04":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "SUCCESS":
          return "success";
        case "FAILURE":
          return "danger";
        case "03":
          return "info";
        case "04":
          return "warning";
        default:
          break;
      }
    },
  },
};
</script>
