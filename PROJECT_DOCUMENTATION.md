# YOAF-Web 项目文档

## 项目概述

**项目名称**: YOAF-Web (新权限管理系统)  
**版本**: 1.1.2  
**技术栈**: Vue.js 2.7.14 + Element UI + Vuex + Vue Router  
**开发环境**: Node.js >= 8.9, npm >= 3.0.0  
**许可证**: MIT  

## 项目简介

YOAF-Web 是一个基于 Vue.js 的企业级管理系统，主要用于金融机构的监管账户管理、批量交易处理、支付指令管理等业务场景。系统采用前后端分离架构，提供完整的权限管理和业务流程支持。

## 核心功能模块

### 1. 监管账户管理 (XZP)
- **账户信息管理**: 监管账户的新增、修改、查询、删除
- **账户审批流程**: 监管账户信息变动的审核机制
- **批量导入导出**: 支持Excel格式的批量数据处理
- **状态管理**: 账户状态的批量修改和跟踪

### 2. 批量交易处理
- **批量交易控制**: 批量交易的状态控制和管理
- **交易明细查询**: 详细的交易记录查询和统计
- **外联批次管理**: 外部系统批次状态的调整和同步
- **交易日志**: 完整的交易操作日志记录

### 3. 支付管理
- **支付指令管理**: 监管账户支付指令的处理
- **欠费库管理**: 欠费记录的查询和状态管理
- **敏感信息处理**: 支付相关敏感数据的脱敏显示

### 4. 对账清算
- **对账不一致处理**: 对账和清算不一致记录的管理
- **数据导出**: 对账结果的导出功能
- **异常处理**: 对账异常情况的处理流程

### 5. 系统管理
- **用户权限管理**: 基于角色的权限控制
- **菜单管理**: 动态菜单配置
- **字典管理**: 系统字典数据维护
- **审计日志**: 系统操作日志记录

## 技术架构

### 前端技术栈
- **Vue.js 2.7.14**: 渐进式JavaScript框架
- **Element UI 2.15.6**: 企业级UI组件库
- **Vuex 3.6.0**: 状态管理
- **Vue Router 3.4.9**: 路由管理
- **Axios 0.24.0**: HTTP客户端
- **ECharts 4.9.0**: 数据可视化
- **yo-ui**: 自定义业务组件库

### 开发工具
- **Vue CLI 4.4.6**: 项目脚手架
- **ESLint**: 代码质量检查
- **Sass**: CSS预处理器
- **Webpack**: 模块打包工具
- **Babel**: JavaScript编译器

### 项目结构
```
src/
├── api/                 # API接口定义
├── assets/             # 静态资源
├── components/         # 公共组件
├── directive/          # 自定义指令
├── layout/            # 布局组件
├── plugins/           # 插件配置
├── router/            # 路由配置
├── store/             # Vuex状态管理
├── utils/             # 工具函数
└── views/             # 页面组件
    ├── xzp/           # 新中平业务模块
    ├── user/          # 用户管理
    ├── role/          # 角色管理
    └── ...
```

## 开发指南

### 环境要求
- Node.js >= 8.9
- npm >= 3.0.0
- 现代浏览器支持

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
访问地址: http://localhost:80

### 生产构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 配置说明

### 环境配置
项目支持多环境配置，通过环境变量控制：
- 开发环境: `NODE_ENV=development`
- 生产环境: `NODE_ENV=production`
- 预发布环境: `NODE_ENV=staging`

### 代理配置
开发环境下配置了多个后端服务代理：
- 新中平服务: `http://127.0.0.1:9091/`
- 审批服务: `http://127.0.0.1:8446/`
- 主服务: `http://127.0.0.1:9086/`

### 构建优化
- Gzip压缩
- 代码分割
- Tree Shaking
- 生产环境去除console.log

## 业务特性

### 数据安全
- 敏感数据脱敏显示
- 权限控制访问
- 操作日志记录
- 数据加密传输

### 用户体验
- 响应式设计
- 表单验证优化
- 加载状态提示
- 错误处理机制

### 性能优化
- 懒加载路由
- 组件按需加载
- 图片压缩
- 缓存策略

## 部署说明

### 构建产物
```
dist/
├── static/            # 静态资源
├── index.html         # 入口页面
└── ...
```

### 部署路径
生产环境部署路径: `/uepsxmzhbgweb`

### 服务器配置
需要配置反向代理支持单页应用路由。

## 开发规范

### 代码规范
- 使用ESLint进行代码检查
- 遵循Vue.js官方风格指南
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### Git规范
- 使用Husky进行提交前检查
- 提交信息遵循约定式提交规范
- 代码审查机制

### 组件开发
- 单一职责原则
- 可复用性设计
- 完善的属性文档
- 事件命名规范

## 常见问题

### 开发环境问题
1. **端口冲突**: 修改vue.config.js中的port配置
2. **代理失败**: 检查后端服务是否启动
3. **依赖安装失败**: 清除node_modules重新安装

### 构建问题
1. **内存不足**: 增加Node.js内存限制
2. **路径错误**: 检查publicPath配置
3. **资源404**: 确认静态资源路径

## 更新日志

### v1.1.2
- 优化监管账号表单验证体验
- 修复数据选择后校验信息不清除的问题
- 新增统一的表单验证清除方法
- 隐藏部分表单字段简化操作界面

## API接口说明

### 监管账户管理接口
- `POST /api/admin/xzp/queryAcctInfoList` - 账户信息分页查询
- `POST /api/admin/xzp/addAcctInfo` - 新增账户信息
- `POST /api/admin/xzp/updateAcctInfo` - 修改账户信息
- `POST /api/admin/xzp/deleteAcctInfo` - 删除账户信息

### 批量交易接口
- `POST /api/admin/xzp/batchTranCtrlList` - 批量交易控制查询
- `POST /api/admin/xzp/batchTranDtlList` - 批量交易明细查询
- `POST /api/admin/xzp/batchUpdateTxnStaToZero` - 批量调整交易状态

### 支付管理接口
- `POST /api/admin/xzp/payOweList` - 欠费库记录查询
- `POST /api/admin/xzp/supAcctPayInstList` - 支付指令查询

## 数据字典

### 交易状态 (txn_status)
- `0` - 欠费
- `1` - 已缴费
- `2` - 处理中
- `9` - 异常

### 账户状态 (acct_status)
- `A` - 正常
- `C` - 关闭
- `F` - 冻结
- `S` - 暂停

### 审批状态 (approval_status)
- `0` - 待审批
- `1` - 审批通过
- `2` - 审批拒绝

## 安全机制

### 权限控制
- 基于角色的访问控制(RBAC)
- 菜单级权限控制
- 按钮级权限控制
- 数据级权限控制

### 数据加密
- 前端使用SM2/SM3国密算法
- 敏感数据传输加密
- 密码加密存储
- Token机制认证

### 审计日志
- 用户操作日志
- 系统异常日志
- 数据变更日志
- 登录访问日志

## 监控告警

### 性能监控
- 页面加载时间
- API响应时间
- 资源使用情况
- 错误率统计

### 业务监控
- 交易成功率
- 账户操作频次
- 异常数据统计
- 用户活跃度

## 测试策略

### 单元测试
- 工具函数测试
- 组件逻辑测试
- API接口测试
- 状态管理测试

### 集成测试
- 页面功能测试
- 业务流程测试
- 权限控制测试
- 数据一致性测试

### 端到端测试
- 用户场景测试
- 浏览器兼容性测试
- 性能压力测试
- 安全渗透测试

## 运维指南

### 日志管理
- 应用日志分级
- 日志轮转策略
- 日志分析工具
- 异常告警机制

### 备份策略
- 代码版本备份
- 配置文件备份
- 数据库备份
- 静态资源备份

### 发布流程
1. 代码提交审查
2. 自动化测试
3. 构建打包
4. 预发布验证
5. 生产环境发布
6. 发布后验证

## 扩展开发

### 新增业务模块
1. 创建API接口文件
2. 定义数据模型
3. 开发页面组件
4. 配置路由权限
5. 编写测试用例

### 组件开发规范
```javascript
// 组件模板
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    }
  },
  methods: {
    // 方法定义
  }
}
</script>

<style scoped>
.component-name {
  /* 样式定义 */
}
</style>
```

## 联系方式

- 开发团队: front-end
- 项目仓库: http://*************/root/yoaf-web-*******.git
- 技术支持: 请通过GitLab Issues提交问题

## 附录

### 浏览器兼容性
- Chrome >= 60
- Firefox >= 55
- Safari >= 11
- Edge >= 79
- IE >= 11 (有限支持)

### 第三方依赖
- Element UI: UI组件库
- ECharts: 图表库
- Axios: HTTP客户端
- Moment.js: 日期处理
- Lodash: 工具函数库

---

*本文档最后更新时间: 2025-01-21*
*文档版本: v1.0.0*
