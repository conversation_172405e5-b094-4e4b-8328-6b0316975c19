-- 测试状态查询SQL
-- 用于验证逻辑删除功能是否正常工作

-- 1. 查看表结构和数据
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        WHEN oth_msg1_tx IS NULL OR oth_msg1_tx != '1' THEN '正常'
        ELSE oth_msg1_tx
    END as status_desc
FROM public.tb_zjjg_acct_info 
ORDER BY cpab_acc_id, acct_nm
LIMIT 10;

-- 2. 测试查询正常状态的记录（othMsg1Tx = '0'）
SELECT COUNT(*) as normal_count
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1');

-- 3. 测试查询已删除状态的记录（othMsg1Tx = '1'）
SELECT COUNT(*) as deleted_count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1';

-- 4. 测试更新一条记录为删除状态
-- UPDATE public.tb_zjjg_acct_info 
-- SET oth_msg1_tx = '1' 
-- WHERE cpab_acc_id = '你的测试账户' AND acct_nm = '你的测试账户名';

-- 5. 测试恢复一条记录为正常状态
-- UPDATE public.tb_zjjg_acct_info 
-- SET oth_msg1_tx = NULL 
-- WHERE cpab_acc_id = '你的测试账户' AND acct_nm = '你的测试账户名';

-- 6. 验证查询条件逻辑
-- 模拟前端传入 othMsg1Tx = '1' 的查询
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1'
ORDER BY cpab_acc_id, acct_nm;

-- 模拟前端传入 othMsg1Tx = '0' 的查询
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
ORDER BY cpab_acc_id, acct_nm;

-- 7. 检查是否有数据的oth_msg1_tx字段值
SELECT DISTINCT oth_msg1_tx, COUNT(*) as count
FROM public.tb_zjjg_acct_info
GROUP BY oth_msg1_tx
ORDER BY oth_msg1_tx;
