-- 创建测试数据脚本
-- 用于测试逻辑删除功能

-- 1. 首先检查当前数据状态
SELECT 
    CASE 
        WHEN oth_msg1_tx IS NULL THEN 'NULL'
        WHEN oth_msg1_tx = '' THEN 'EMPTY'
        WHEN oth_msg1_tx = '0' THEN 'ZERO'
        WHEN oth_msg1_tx = '1' THEN 'ONE'
        ELSE 'OTHER:' || oth_msg1_tx
    END as status_value,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
GROUP BY oth_msg1_tx
ORDER BY count DESC;

-- 2. 查看前5条记录的详细信息
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        WHEN oth_msg1_tx IS NULL OR oth_msg1_tx != '1' THEN '正常'
        ELSE '未知状态:' || oth_msg1_tx
    END as status_desc
FROM public.tb_zjjg_acct_info
ORDER BY cpab_acc_id, acct_nm
LIMIT 5;

-- 3. 创建一条测试数据（设置为已删除状态）
-- 请根据上面查询的结果，选择一条记录进行更新
UPDATE public.tb_zjjg_acct_info 
SET oth_msg1_tx = '1' 
WHERE cpab_acc_id = (
    SELECT cpab_acc_id 
    FROM public.tb_zjjg_acct_info 
    WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    ORDER BY cpab_acc_id 
    LIMIT 1
)
AND acct_nm = (
    SELECT acct_nm 
    FROM public.tb_zjjg_acct_info 
    WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    ORDER BY cpab_acc_id 
    LIMIT 1
);

-- 4. 验证更新结果
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        ELSE '正常'
    END as status_desc
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1'
ORDER BY cpab_acc_id, acct_nm;

-- 5. 测试查询条件 - 查询已删除记录
SELECT COUNT(*) as deleted_count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1';

-- 6. 测试查询条件 - 查询正常记录
SELECT COUNT(*) as normal_count
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1');

-- 7. 如果需要恢复测试数据，执行以下SQL
-- UPDATE public.tb_zjjg_acct_info 
-- SET oth_msg1_tx = NULL 
-- WHERE oth_msg1_tx = '1';

-- 8. 验证MyBatis条件的SQL等价性
-- 这个查询应该和MyBatis中 queryVo.othMsg1Tx.equals("1") 的结果一致
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1'
ORDER BY cpab_acc_id, acct_nm
LIMIT 10;
