import validate from "@/utils/validate"
export default {
  index:true,
  indexLabel:'序号',
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchLabelWidth:120,
  searchMenuPosition: 'left',
  addTitle: '新增本地授权关系',
  editTitle: '修改本地授权关系',
  // saveBtnText: '确定',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  tip:false,
  columnBtn:false,
  column: [
    {
      label: '操作角色',
      prop: 'operRoleId',
      search: true,
      formslot: true,
      rules: [{
        required: true,
        message: "请选择操作角色",
        trigger: "change"
      }],
    },
    {
      label: '操作角色名称',
      prop: 'operRoleName',
      search: true,
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '授权角色',
      prop: 'authRoleId',
      formslot: true,
      rules: [{
        required: true,
        message: "请选择授权角色",
        trigger: "change"
      }],
    },
    {
      label: '授权角色名称',
      prop: 'authRoleName',
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '功能名称',
      prop: 'funcId',
      formslot: true,
      hide: true,
      showColumn: false,
      rules: [{
        required: true,
        message: "请选择功能名称",
        trigger: "change"
      }],
    },
    {
      label: '功能名称',
      prop: 'funcName',
      search: true,
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '请求地址',
      prop: 'funcURI',
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '创建人',
      prop: 'createUser',
      addDisplay: false,
      editDisplay:false
    },
    {
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisplay:false
    },
  ]
}
