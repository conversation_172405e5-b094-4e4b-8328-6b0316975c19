import request from '@/utils/request'

/**
 * 2022/7/18
 * 分页查询机构列表接口
 * @param {instId:机构编号,instNameAbbr:机构简称,upInstId:上级机构编号,orgStatus:机构状态，pageNum：当前页,pageSize：每页条数}
 */
export function instsList (param) {
    const { currentPage = 1, pageSize = 10, instId = '', instNameAbbr = '',upInstId = '',orgStatus = '' } = param || {}
    return request({
      url: `/api/admin/insts/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
      method: 'post',
      data: {
        instId,
        instNameAbbr,
        upInstId,
        orgStatus,
      }
    })
  }
// 新增机构接口
export function addInsts(param) {
  const { instId = '', instName = '', instNameAbbr = '', instEname = '',upInstId = '',instLvl = '',
  instKind = '', brhType = '', brhSubType = '',instAddress = '',instZipNo = '', instInChargeTlrNo = '',
   instInChargePhone = '', instInChargeName = '',orgStatus = '' } = param || {}
  return request({
    url: '/api/admin/insts/actions/add',
    method: 'post',
    data: {
      instId,
      instName,
      instNameAbbr,
      instEname,
      upInstId,
      instLvl,
      instKind,
      brhType,
      brhSubType,
      instAddress,
      instZipNo,
      instInChargeTlrNo,
      instInChargePhone,
      instInChargeName,
      orgStatus,
    }
  })
}

// 修改更新机构接口
export function editInsts(param) {
  const { instId = '', instName = '', instNameAbbr = '', instEname = '',upInstId = '',instLvl = '',
  instKind = '', brhType = '', brhSubType = '',instAddress = '',instZipNo = '', instInChargeTlrNo = '',
   instInChargePhone = '', instInChargeName = '',orgStatus = '' } = param || {}
  return request({
    url: '/api/admin/insts/actions/edit',
    method: 'post',
    data: {
      instId,
      instName,
      instNameAbbr,
      instEname,
      upInstId,
      instLvl,
      instKind,
      brhType,
      brhSubType,
      instAddress,
      instZipNo,
      instInChargeTlrNo,
      instInChargePhone,
      instInChargeName,
      orgStatus,
    }
  })
}

// 查询单个机构详情
export function getInsts (instId) {
  return request({
    url: '/api/admin/insts/actions/get/' + instId,
    method: 'get'
  })
}

// 删除机构
export function delInsts (instId) {
  return request({
    url: '/api/admin/insts/actions/remove/'+ instId,
    method: 'post',
  })
}
//导出机构信息接口
export function exportInsts(data) {
  return request({
    url: '/api/admin/insts/actions/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//下载机构导入模板
export function downloadExample(data) {
  return request({
    url: '/api/admin/insts/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob'
  })
}

//导入机构信息接口
export function importInsts(data) {
  return request({
    url: '/api/admin/insts/actions/import',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}
//分页查询机构下用户接口
export function instsUserList (param) {
  const { currentPage = 1, pageSize = 10, instId = '', userName = '',userNickname = '' ,userStat = '',empStatus = ''} = param || {}
  return request({
    url: `/api/admin/insts/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      instId,
      userName,
      userNickname,
      userStat,
      empStatus,
    }
  })
}
//分页查询非机构下用户接口
export function undeployUserList (param) {
  const { currentPage = 1, pageSize = 10, instId = '', userName = '',userNickname = '' ,userStat = '',empStatus = ''} = param || {}
  return request({
    url: `/api/admin/insts/no/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      instId,
      userName,
      userNickname,
      userStat,
      empStatus,
    }
  })
}
//7.21保存机构下用户接口
export function addUserList (param) {
  return request({
    url: `/api/admin/insts/users/actions/add`,
    method: 'post',
    data: param
  })
}

//删除机构下用户接口
export function delUserList (param) {
  return request({
    url: `/api/admin/insts/users/actions/remove`,
    method: 'post',
    data: param
  })
}
