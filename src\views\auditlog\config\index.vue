<template>
  <div class="app-container">
    <yo-table
      v-loading="loading"
      :data="data"
      :option="tableOption"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @row-update="rowUpdate"
      @row-save="rowSave"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <template slot-scope="{ scope }" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['audit:log:config:page']"
          >查询</el-button
        >
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()"
          >重置</el-button
        >
      </template>
      <!-- 自定义右侧操作栏 -->
      <template slot-scope="{ size }" slot="menuRight">
        <input
          id="fileslist"
          v-show="false"
          type="file"
          accept=".xls"
          ref="fileRef"
          @change="fileChange"
        />
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="exportExample"
          v-hasPermi="['audit:log:config:download']"
          >下载模板</el-button
        >
        <el-button
          type="warning"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['audit:log:config:import']"
          >导入</el-button
        >
        <el-button
          type="success"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['audit:log:config:export']"
          >导出</el-button
        >
      </template>
      <!-- 自定义列 -->
      <template slot="status" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{
          userStat(scope.row)
        }}</el-tag>
      </template>
      <!-- 自定义左侧操作栏 -->
      <template slot-scope="{ size }" slot="menuLeft">
        <el-button
          type="primary"
          icon="el-icon-plus"
          :size="size"
          plain
          v-hasPermi="['audit:log:config:add']"
          @click.stop="handleAdd"
          >新增</el-button
        >
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :size="size"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['audit:log:config:remove']"
          >批量删除</el-button
        >
      </template>
      <!-- 自定义操作栏 -->
      <template slot-scope="{ row, size, type, index }" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-edit"
          @click.stop="handleEdit(row, index)"
          v-hasPermi="['audit:log:config:edit']"
          >修改</el-button
        >
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-delete"
          @click="handleDelete(row)"
          v-hasPermi="['audit:log:config:remove']"
          >删除</el-button
        >
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-info"
          @click="handleCheckInfo(row)"
          v-hasPermi="['audit:log:config:get']"
          >查看</el-button
        >
      </template>
    </yo-table>
  </div>
</template>
<script>
import {
  listConfig,
  addConfig,
  delConfig,
  delConfigs,
  getConfigId,
  updateConfig,
  onOffConfig,
  exportConfig,
  importConfig,
  downloadExample,
} from "@/api/system/auditlog/config";
import tableOption from "./infoData/tableoption.js";

export default {
  name: "Auditlog",
  dicts: [
    "yoaf_system_code",
    "yoaf_log_state",
    "yoaf_request_action",
    "yoaf_request_type",
  ],
  data() {
    return {
      loading: true,
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      data: [],
      tableOption: tableOption,
      // 非多个禁用
      multiple: true,
    };
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.tableOption.column,
      "systemCode",
      this.dict.type["yoaf_system_code"]
    );
    this.updateDictData(
      this.tableOption.column,
      "status",
      this.dict.type["yoaf_log_state"]
    );
    this.updateDictData(
      this.tableOption.column,
      "action",
      this.dict.type["yoaf_request_action"]
    );
    this.updateDictData(
      this.tableOption.column,
      "requestType",
      this.dict.type["yoaf_request_type"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    /** 分页查询审计日志列表 */
    getList(query) {
      const params = { ...query, ...this.page };
      this.loading = true;
      listConfig(params).then((response) => {
        if (response.code == 0) {
          this.data = response.data.list;
          this.page.total = response.data.total;
          this.loading = false;
        } 
      });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      addConfig(form).then((response) => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.getList();
          done();
        } 
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      console.log(this.search);
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.names = selection.map((item) => item.message);
      this.multiple = !selection.length;
    },

    // 修改
    async handleEdit(row, index) {
      const id = row.id;
      await getConfigId(id).then((response) => {
        const data = response.data;
        this.formParent = data;
        this.$refs.crud.rowEdit(data, index);
      });
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      updateConfig(form).then((response) => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.getList();
          done();
        } 
      });
    },
    //查看详情
    handleCheckInfo(row) {
      const id = row.id;
      getConfigId(id).then((response) => {
        this.$refs.crud.rowView(response.data);
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      const names = row.message || this.names
      const h = this.$createElement;
      this.$confirm(`是否永久删除交易名称为" ${names} "的数据项？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        showCancelButton: true,
      })
      // this.$msgbox({
      //   title: "提示",
      //   message: h("p", null, [
      //     h(
      //       "p",
      //       { style: "word-break: break-all" },
      //       "是否永久删除id为" + id + "的数据项？"
      //     ),
      //   ]),
      //   showCancelButton: true,
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      // })
        .then(() => {
          if (row.id) {
            this.handleDelUser(id, delConfig);
          } else {
            this.handleDelUser(id, delConfigs);
          }
        })
        .catch(() => {});
    },
    async handleDelUser(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getList();
      }
    },
    // 状态修改
    handleStatusChange(row) {
      const data = {
        id: row.id,
        status: row.status,
      };
      let text = row.status === "1" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '"id为"' + row.id + '"的配置吗？')
        .then(() => {
          return onOffConfig(data);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(() => {
          row.status = row.status === "1" ? "2" : "1";
        });
    },
    /** 处理返回的流文件 */
    handleExportData(res) {
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: "application/octet-stream" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    //导出
    handleExport() {
      const data = { ids: this.ids, ...this.exportSearch };
      exportConfig(data).then((res) => {
        this.handleExportData(res);
      });
    },
    /** 下载模板 */
    exportExample() {
      downloadExample().then((res) => {
        this.handleExportData(res);
      });
    },
    fileChange(event) {
      let file = document.querySelector("#fileslist").files[0];
      let formData = new FormData();
      formData.append("file", file);
      importConfig(formData).then((res) => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.getList();
        }
      });
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport() {
      this.$refs.fileRef.click();
    },
    userStat(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "03":
          return "注销";
        case "04":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "03":
          return "info";
        case "04":
          return "warning";
        default:
          break;
      }
    },
  },
};
</script>
