<template>
  <el-dialog :visible.sync="visible"
             destroy-on-close
             :beforeClose="beforeClose"
             v-bind="dialog">
    <div style="padding-bottom: 10px;">
      <el-row>
        <el-alert type="error">
          <template slot="title">
            <i class="el-icon-warning"></i>
            <span>友情提示：{{ tipInfo }}</span>
          </template>
        </el-alert>
      </el-row>
    </div>
    <yo-form :option="form.option"
               v-model="form.data"
               @submit="submit"
               @reset-change="resetChange"></yo-form>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      // 本地授权框是否展示
      visible: false,
      // 弹框属性配置
      dialog: {
        // 点击空白是否关闭
        closeOnClickModal: false
      },
      // 回调1（then）
      resolve: null,
      // 回调2（then）
      reject: null,
      tipInfo: '请选择合适的角色对应账号进行本地授权',
      // 表单模型数据
      form: {
        option: {
          submitText: '提交',
          emptyText: '关闭',
          emptyIcon: 'el-icon-close',
          column: []
        },
        data: {}
      }
    };
  },
  methods: {
    beforeClose (done) {
      done()
      this.close()
    },
    /**
     * 显示本地授权弹框（入口）
     * @param opt
     * @param resolve
     * @param reject
     */
    show (opt, resolve, reject) {
      // 点击提交回调
      this.resolve = resolve;
      // 点击关闭回调
      this.reject = reject;
      this.dialog = Object.assign(this.dialog, opt);
      this.form.option = Object.assign(this.form.option, opt.option);
      this.tipInfo = opt?.tipInfo || '请选择合适的角色对应账号进行本地授权'
      this.form.data = opt.data;
      this.visible = true;
    },
    /**
     * 关闭本地授权弹框（回调关闭）
     */
    close () {
      if (typeof this.dialog.beforeClose === 'function') {
        this.dialog.beforeClose();
      }
      this.visible = false;
      this.$destroy();
      this.$el.remove();
    },
    /**
     * 点击提交按钮则回调1（pending -> fulfilled）
     * @param data
     * @param done
     */
    submit (data, done) {
      done()
      this.resolve({ data: data, close: this.close, done: done });
    },
    /**
     * 点击关闭按钮则回调2（pending -> rejected）
     */
    resetChange() {
      this.reject({data: this.form.data, close: this.close})
    }
  }
};
</script>
