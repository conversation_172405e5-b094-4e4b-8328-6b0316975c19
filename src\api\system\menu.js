import request from '@/utils/request'

// 查询菜单树列表
export function listMenu(query) {
  return request({
    url: '/api/admin/menus/actions/tree',
    method: 'post',
    params: query
  })
}

// 查询菜单详细功能
export function getMenu(data) {
  const { funcBtn = "", funcName = "",funcURI = "",menuId = "", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/functions/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      funcBtn,
      funcName,
      funcURI,
      menuId
    }
  })
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/api/admin/menus/actions/add',
    method: 'post',
    data: data
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/api/admin/menus/actions/edit',
    method: 'post',
    data: data
  })
}

// 删除菜单
export function delMenu(Id) {
  return request({
    url: '/api/admin/menus/actions/remove/' + Id,
    method: 'post'
  })
}

// 新增一个功能
export function addTable(data) {
  return request({
    url: '/api/admin/functions/actions/add',
    method: 'post',
    data
  })
}

// 查询一个功能
export function getOneTable(id) {
  return request({
    url: '/api/admin/functions/actions/get/' + id,
    method: 'get',
  })
}
// 更新一个功能
export function editOneTable(data) {
  return request({
    url: '/api/admin/functions/actions/edit',
    method: 'post',
    data
  })
}

// 删除表格一项
export function delTable(Id) {
  return request({
    url: '/api/admin/functions/actions/remove/' + Id,
    method: 'post'
  })
}
// 表格批量删除
export function delTables(data) {
  return request({
    url: '/api/admin/functions/actions/remove',
    method: 'post',
    data
  })
}