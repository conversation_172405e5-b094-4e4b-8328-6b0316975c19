<!--批量交易控制表-->
<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" ref="search" class="search-form" :rules="rules" label-width="120px" size="small">
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="不符类型" prop="recordType" required: true>
            <el-select v-model="search.recordType" clearable placeholder="请选择不符类型" style="width: 200px;">
              <el-option
                v-for="dict in dict.type.xzp_record_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="银联清算日期" prop="merchDt" required: true >
            <el-date-picker style="width:200px" v-model="search.merchDt" type="date" value-format="yyyyMMdd" placeholder="请选择交易日期" clearable />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter="24" style="margin-top: 0;">
        <el-col :span="10">
          <el-form-item label="交易状态">
            <el-select v-model="search.recordSta" clearable placeholder="请选择交易状态" style="width: 200px;">
              <el-option
                v-for="dict in dict.type.xzp_record_sta"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>  
        <el-col :span="24" class="search-btns">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    </el-card>
      
      
      <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      :before-open="beforeOpen"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      @row-update="rowUpdate"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <!-- 自定义左侧操作栏 -->
      <template slot-scope="{size}" slot="menuLeft">
        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['admin:recSettMismatch:export']">
	        导出
        </el-button>
      </template>
      <!-- 自定义操作按钮 -->
       <template slot-scope="{row,size,type}" slot="menu">
        <el-button :size="size" :type="type" icon="el-icon-edit" @click.stop="handleUpdate(row)" v-hasPermi="['admin:recSettMismatch:edit']">
          编辑
        </el-button>
        
        <el-button :size="size" :type="type" @click="handleCommand(row)" icon="el-icon-info">
          查看
        </el-button>
      </template>

    </yo-table>
    
  </div>
</template>

<script>
import { recSettMismatchList, updateRecSettMismatch,exportRecSettMismatch} from '@/api/recSettMismatch';
import recSettMismatchTableOption from './infoData/recSettMismatchTableOption.js';

export default {
  name: 'recSettMismatch',
  dicts: ['xzp_record_type','xzp_record_sta'],
  data() {
    return {
      loading: false,
      formParent: {},
      search: {
        recordType: '',
        merchDt: '',
        recordSta: ''
      },
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      data: [],
      option: recSettMismatchTableOption,
      multiple: true,
      // 自动完成相关数据
      opeCdOptions: [], // 业务代码选项
      merchIdOptions: [], // 委托单位代码选项
      opeCdLoading: false,
      merchIdLoading: false,
      rules: {
        recordType: [
          { required: true, message: '请选择不符类型', trigger: 'change' }
        ],
        merchDt: [
          { required: true, message: '请选择银联清算日期', trigger: 'change' }
        ]
      },
    };
  },
  created() {
    this.page.currentPage = 1;
    this.getList();
    // 更新字典数据
    this.updateDictData(this.option.column, 'recordType', this.dict.type.xzp_record_type);
    this.updateDictData(this.option.column, 'recordSta', this.dict.type.xzp_record_sta);
  },
  methods: {
    // 更新字典数据
    updateDictData(option, key, data) {
      // 更新表格配置中的状态选项
      const column = this.findObject(option, key);
      if (column) {
        column.dicData = data;
      }
    },
    handleCommand(row) {
      this.$refs.crud.rowView(row);
    },
    // 查找对象
    findObject(array, prop) {
      return array.find(item => item.prop === prop);
    },
    onLoad() {
      this.getList();
    },
    beforeOpen(done, type) {
      done();
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    async getList(query) {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      try {
        const { code, data } = await recSettMismatchList(params);
        if (code === '0') {
          this.data = data.list;
          this.page.total = data.total;
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.$refs['search'].validate(valid => {
        if (valid) {
          this.page.currentPage = 1;
          this.getList();
        } else {
          //this.$message.error('请正确填写查询条件');
          return false;
        }
      });
    },
    resetQuery() {
      this.search = {
        recordType: '',
        merchDt: '',
        recordSta: ''
      };
      this.opeCdOptions = [];
      this.merchIdOptions = [];
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.multiple = !(selection.length > 0);
    },
    // // 处理编辑
    handleUpdate(row) {
      this.formParent = JSON.parse(JSON.stringify(row));
      this.$refs.crud.rowEdit({
        ...row
      });
    },
    
    // 更新数据
    async rowUpdate(row, index, done, loading) {
      try {
        const { code } = await updateRecSettMismatch(row);
        if (code === '0') {
          this.$message.success('更新成功');
          this.getList();
        } else {
          this.$message.error('更新失败');
        }
      } catch (error) {
        console.error('Error updating data:', error);
        this.$message.error('更新失败');
      } finally {
        done();
        loading();
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$refs['search'].validate(valid => {
        if (valid) {
          const data = {  ...this.search };
          exportRecSettMismatch(data).then(res => {
            this.handleExportData(res);
          });
        } else {
          //this.$message.error('请正确填写查询条件');
          return false;
        }
      });
    },
    /** 处理返回的流文件 */
    handleExportData(res) {
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: "application/x-excel" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}
.search-btns {
  text-align: right;
  padding-right: 50px;
}
.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 