import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增系统',
    viewTitle: '查看详情',
    editTitle: '修改系统',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    // excelBtn:true,
    column: [
        {
            label: '源名称',
            width: 120,
            prop: 'source',
            span: 24, //一行
            search: true,
            rules: [
                {
                    required: true,
                    message: '请输入源名称',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 100,
                    message: '长度在 1 到 100 个字符',
                    trigger: 'blur',
                },
            ],
        },
        {
          label: '企业ID',
          prop: 'clientId',
          span: 24, //一行
          rules: [
            {
              required: true,
              message: '请输入企业ID',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 32,
              message: '长度在 1 到 32 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '应用AgentId',
          prop: 'agentId',
          span: 24, //一行
          search: true,
          searchLabelWidth: 100, //左边长度
          rules: [
            {
              required: true,
              message: '请输入应用id',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 32,
              message: '长度在 1 到 32 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
            {
              validator: validate.isNumber,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '密钥Secret',
          prop: 'clientSecret',
          span: 24, //一行
          type: "textarea",
          rules: [
            {
              required: true,
              message: '请输入密钥',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 120,
              message: '长度在 1 到 120 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '回调地址',
          prop: 'redirectUri',
          span: 24, //一行
          type: "textarea",
          rules: [
            {
              required: true,
              message: '请输入回调地址',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 200,
              message: '长度在 1 到 200 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
          ],
        },
    ],
};
