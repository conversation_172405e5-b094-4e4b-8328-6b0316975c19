<template>
  <div>
    <el-container v-if="showContainer">
      <inst-register ref="register" @ok="refreshList" />
      <el-aside>
        <el-input
          v-model="filterText"
          placeholder="搜索关键字"
          clearable
          size="small"
          style="margin-bottom: 10px;"
        ></el-input>
        <el-tree
        v-loading="treeLoading"
          ref="tree"
          node-key="id"
          :props="treeprops"
          :load="treeloadNode"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultexpanded"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          lazy
        ></el-tree>
      </el-aside>
      <el-main style="padding-top:0px;">
        <el-tabs>
          <el-tab-pane class="app-container">
            <span slot="label">{{treeName}}</span>
            <yo-table
              v-loading="tableLoading"
              :data="tabledata"
              :option="tableoption"
              ref="crud"
              :page.sync="page"
              :search.sync="search"
              @on-load="onLoad"
              :before-open="beforeOpen"
              @row-update="rowUpdate"
              @row-save="rowSave"
              v-model="formParent"
              @selection-change="handleSelectionChange"
              @search-change="searchChange"
            >
              <!-- 自定义表单 -->
              <template slot-scope="{scope}" slot="upInstIdForm">
                <el-cascader
                  ref="upInstIdFormRef"
                  v-model="formParent.upInstId"
                  :show-all-levels="false"
                  :options="treedata"
                  :props="props"
                  clearable
                  placeholder="请选择上级机构"
                  @change="closeUpInstIdCascader()"
                ></el-cascader>
              </template>
              <template slot-scope="{scope}" slot="brhTypeForm">
                <el-select
                  v-model="formParent.brhType"
                  @change="brhTypeChange"
                  clearable
                  :disabled="typeDisabled"
                  placeholder="请选择机构类别"
                >
                  <el-option
                    v-for="dict in dict.type['yoaf_brh_type']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
              <template slot-scope="{scope}" slot="brhSubTypeForm">
                <el-select
                  v-model="formParent.brhSubType"
                  no-data-text="请先选择机构类别"
                  clearable
                  :disabled="typeDisabled"
                  placeholder="请选择机构子类别"
                >
                  <el-option
                    v-for="dict in brhSubTypeList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
              <template slot-scope="scope" slot="orgStatusForm">
                <el-select
                  :disabled="typeDisabled"
                  v-model="formParent.orgStatus"
                  placeholder="请选择 机构状态"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type['yoaf_org_status']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>

              <template slot-scope="{scope}" slot="searchMenu">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-search"
                  @click.stop="handleQuery"
                  v-hasPermi="['admin:insts:page']"
                >查询</el-button>
                <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
              </template>
              <template slot="orgStatus" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{orgStatus(scope.row)}}</el-tag>
              </template>
              <!-- 自定义左侧操作栏 -->
<!--              <template slot-scope="{size}" slot="menuLeft">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="mini"
                  plain
                  v-hasPermi="['admin:insts:add']"
                  @click.stop="handleAdd"
                >新增</el-button>
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-caret-top"
                  size="mini"
                  @click="handleRegister"
                  v-hasPermi="['admin:inst:register']"
                >注册</el-button>
              </template>-->
              <!-- 自定义右侧操作栏 -->
<!--              <template slot-scope="{size}" slot="menuRight">
                <input
                  id="fileslist"
                  v-show="false"
                  type="file"
                  accept=".xls"
                  ref="fileRef"
                  @change="fileChange"
                />
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  size="mini"
                  @click="exportExample"
                  v-hasPermi="['admin:insts:download']"
                >下载模板</el-button>
                <el-button
                  type="warning"
                  icon="el-icon-upload2"
                  size="mini"
                  @click="handleImport"
                  v-hasPermi="['admin:insts:import']"
                >导入</el-button>
                <el-button
                  type="success"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['admin:insts:export']"
                >导出</el-button>
              </template>-->
              <template slot-scope="{row,size,type,index}" slot="menu">
<!--                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-edit"
                  @click.stop="handleEdit(row,index)"
                  v-hasPermi="['admin:insts:edit']"
                >修改</el-button>
                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                  v-hasPermi="['admin:insts:remove']"
                >删除</el-button>
                <el-dropdown
                  :size="size"
                  @command="(command) => handleCommand(command, row)"
                  v-hasPermi="['admin:insts:get','admin:user:passwd:reset']"
                >
                  <span class="el-dropdown-link">
                    <i class="el-icon-d-arrow-right el-icon&#45;&#45;right"></i>更多
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="handleCheckInfo"
                      icon="el-icon-info"
                      v-hasPermi="['admin:insts:get']"
                    >查看</el-dropdown-item>
                    <el-dropdown-item
                      command="handleDeployUser"
                      icon="el-icon-s-custom"
                      v-hasPermi="['admin:user:passwd:reset']"
                    >配置用户</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>-->

                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-info"
                  @click="handleCommand('handleCheckInfo', row)"
                  v-hasPermi="['admin:insts:get']"
                >查看</el-button>
                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-s-custom"
                  @click="handleCommand('handleDeployUser', row)"
                  v-hasPermi="['admin:user:passwd:reset']"
                >配置用户</el-button>

              </template>
            </yo-table>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
    <div v-else>
      <deploy-user @show-detail="showDetails" :instId="instId"></deploy-user>
    </div>
  </div>
</template>
<script>
import {
  instsList,
  addInsts,
  getInsts,
  editInsts,
  delInsts,
  exportInsts,
  downloadExample,
  importInsts
} from "@/api/system/mechanism";
import { mapActions } from "vuex";
import tableoption from "./infoData/tableoption.js";
import deployUser from "./component/deployUser";
import instRegister from "./component/instRegister";

export default {
  name: "Mechanism",
  dicts: [
    "yoaf_inst_lvl",
    "yoaf_inst_kind",
    "yoaf_brh_type",
    "yoaf_brh_type_01",
    "yoaf_brh_type_02",
    "yoaf_brh_type_03",
    "yoaf_brh_type_04",
    "yoaf_brh_type_05",
    "yoaf_org_status"
  ],
  components: {
    deployUser,
    instRegister
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  data() {
    var that = this;
    return {
      treeLoading:true,
      filterText: "",
      defaultexpanded: [], //默认展开第一级
      treeName: "全部",
      tableLoading: true,
      showContainer: true, //显示主界面
      form: {},
      formParent: {},
      search: {},
      treedata: [],
      tabledata: [],
      tableoption: tableoption,
      page: {
        pageSize: 10,
        currentPage: 1
      },
      treeprops: {
        label: "name",
        children: "children"
      },
      props: {
        value: "id",
        label: "name",
        checkStrictly: true,
        emitPath: false,
        lazy: true,
        lazyLoad(node, resolve) {
          if (node && resolve) {
            const tree = node.data;
            const query = {
              id: tree.id,
              queryType: tree.queryType
            };
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            that.instDeptTreeChild(query).then(res => {
              resolve(res);
            });
          }
        }
      },
      upInstId: "",
      instId: null,
      brhSubTypeList: [],
      // 非多个禁用
      multiple: true,
      exportSearch:{},
      typeDisabled: false
    };
  },
  computed: {},
  created() {
    this.getTreeList();
    /** *添加字典项数据*/
    this.updateDictData(
      this.tableoption.column,
      "instLvl",
      this.dict.type["yoaf_inst_lvl"]
    );
    this.updateDictData(
      this.tableoption.column,
      "instKind",
      this.dict.type["yoaf_inst_kind"]
    );
    this.updateDictData(
      this.tableoption.column,
      "orgStatus",
      this.dict.type["yoaf_org_status"]
    );
  },
  methods: {
    ...mapActions(["instTree", "instDeptTreeChild"]),
    showDetails() {
      this.showContainer = true;
    },

    // 弹窗打开之前
    beforeOpen(done, type) {
      // 如果是查看,自定义表单禁用
      if (type == "view") {
        this.typeDisabled = true;
        let brhTypes = ''
        if(this.formParent.brhType) {
          brhTypes = "yoaf_brh_type_" + this.formParent.brhType;
        }
        this.formParent.brhType = brhTypes
        this.brhSubTypeList = this.dict.type[brhTypes];
      } else {
        this.typeDisabled = false;
      }
      // 获取下拉字典数据
      //  && this.formParent.brhType
      if (type == 'edit') {
        let brhTypes = ''
        if(this.formParent.brhType) {
          brhTypes = "yoaf_brh_type_" + this.formParent.brhType;
        }
        this.formParent.brhType = brhTypes
        this.brhSubTypeList = this.dict.type[brhTypes];
      } else if(type == 'add') {
        this.brhSubTypeList = [];
      }
      done();
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    /****表单联动方法 */
    brhTypeChange(val) {
      this.formParent.brhSubType = "";
      let brhTypes = val;
      this.formParent.brhType = brhTypes
      this.brhSubTypeList = this.dict.type[brhTypes];
    },

    /***  table方法   ****/
    // 首次加载调用此方法
    onLoad(page) {
      this.getTableList({ upInstId: this.upInstId });
    },
    /**分页查询机构列表*/
    async getTableList(query) {
      const params = { ...query, ...this.page };
      let res = await instsList(params);
      if (res.code == 0) {
        let list = res.data.list;
        list.forEach(item => {
          item.upInstId = item.upInst ? item.upInst["instId"] : "";
          item.upInstNameAbbr = item.upInst ? item.upInst["instNameAbbr"] : "";
        });
        this.tabledata = list;
        this.page.total = res.data.total;
        this.tableLoading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      const search = { upInstId: this.upInstId, ...this.search };
      this.page.currentPage = 1;
      this.getTableList(search);
      this.exportSearch = this.search
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    // 机构表单，选择上级机构后自动关闭级联框
    closeUpInstIdCascader() {
      this.$refs.upInstIdFormRef.dropDownVisible = false
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      let params = JSON.parse(JSON.stringify(form))
      if(params.brhType) {
        params.brhType = form.brhType.slice(-2);
      }
      addInsts(params).then(response => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.resetNode();
          this.handleQuery();
          done();
        }
      });
    },
    // 修改
    async handleEdit(row, index) {
      let res = await getInsts(row.instId);
      if (res.code == 0) {
        let data = res.data;
        data.upInstId = data.upInst ? data.upInst["instId"] : "";
        data.upInstId1 = data.upInst ? data.upInst["instNameAbbr"] : "";
        this.formParent = data;
        this.$refs.crud.rowEdit(data, index);
      }
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      let params = JSON.parse(JSON.stringify(form))
      if(params.brhType) {
        params.brhType = form.brhType.slice(-2);
      }
      editInsts(params).then(response => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
          this.resetNode();
          done();
        }
      });
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleCheckInfo":
          this.handleCheckInfo(row);
          break;
        case "handleDeployUser":
          this.handleDeployUser(row);
          break;
        default:
          break;
      }
    },
    async handleCheckInfo(row) {
      let res = await getInsts(row.instId);
      if (res.code == 0) {
        let data = res.data;
        data.upInstId = data.upInst ? data.upInst["instId"] : "";
        data.upInstId1 = data.upInst ? data.upInst["instNameAbbr"] : "";
        this.formParent = data;
        this.$refs.crud.rowView(data);
      }
    },
    handleDeployUser(row) {
      this.instId = row.instId;
      this.showContainer = false;
    },
    /***多选框选中数据**/
    handleSelectionChange(selection) {
      this.multiple = !selection.length;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h(
            "p",
            { style: "word-break: break-all" },
            '是否确认删除id为"' + row.instId + '"的数据项？'
          )
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          this.handleDel(row.instId, delInsts);
        })
        .catch(() => {});
    },
    async handleDel(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.handleQuery();
        this.resetNode();
      }
    },
    /****注册按钮 */
    handleRegister() {
      this.$refs.register.show();
    },
    /** 处理返回的流文件 */
    handleExportData(res) {
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 存储二进制文件
      let blob = new Blob([data], { type: "application/x-excel" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 下载模板 */
    exportExample() {
      downloadExample().then(res => {
        this.handleExportData(res);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const data = {  ...this.exportSearch };
      exportInsts(data).then(res => {
        this.handleExportData(res);
      });
    },
    fileChange(event) {
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append("file", file);
      importInsts(formData).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.handleQuery();
        }
      });
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport() {
      // this.$refs.fileRef.dispatchEvent(new MouseEvent('click'))
      this.$refs.fileRef.click();
    },
    refreshList() {
      this.handleQuery();
      this.getTreeList();
    },
    /*****以下为 tree方法 */
    /** 查询机构树 */
    getTreeList() {
      this.instTree("inst").then(res => {
        this.treedata = this.filterTree(res);
      });
    },
    // 过滤数据
    filterTree(data) {
      data.forEach(item => {
        item.hasChildren = item.lazy;
        if (item.children) return this.filterTree(item.children);
      });
      return data;
    },
    // 点击节点回调
    handleNodeClick(data) {
      this.treeName = data.name;
      this.upInstId = data.id;
      this.getTableList({ upInstId: data.id });
    },
    /***刷新tree**/
    resetNode(){
      let theChildren = this.rootNode.childNodes
      theChildren.splice(0,theChildren.length)
      this.treeloadNode(this.rootNode,this.rootResolve)
    },
    /***懒加载回调**/
    treeloadNode(node, resolve) {
      if (node.level === 0) {
        this.rootNode = node
        this.rootResolve = resolve
        this.instTree("inst").then(res => {
          res.forEach(item => {
            this.defaultexpanded.push(item.id);
          });
          this.treeLoading = false
          return resolve(res);
        });
      }
      if (node.level == 1) {
        if (node.data.children) return resolve(node.data.children);
        this.treeLoading = false
        return resolve([]);
      }
      if (node.level > 1) {
        const query = {
          id: node.data.id,
          queryType: node.data.queryType
        };
        this.instDeptTreeChild(query).then(res => {
          this.treeLoading = false
          return resolve(res);
        });
      }
    },
    // tree搜索
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    //20240508改造新增字段：机构状态
    orgStatus(row) {
      switch (row.orgStatus) {
        case "running":
          return "运行";
        case "stop":
          return "撤销";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.orgStatus) {
        case "running":
          return "success";
        case "stop":
          return "info";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.el-tree {
  max-height: calc(100vh - 180px) !important;
  min-height: calc(100vh - 300px) !important;
  overflow: auto;
}
</style>
