# 账户管理逻辑删除功能实现说明

## 功能概述

将账户管理页面的物理删除改为逻辑删除，使用`oth_msg1_tx`字段标识删除状态：
- `1` = 已删除
- `0`或`NULL` = 正常状态

同时新增状态查询功能，支持按状态筛选账户记录。

## 修改内容

### 1. 后端修改

#### 1.1 Service层修改
**文件**: `xm-xzp-impl/src/main/java/com/xm/xzp/service/impl/TbZjjgAcctInfoServiceImpl.java`

**修改内容**:
- 修改`deleteAcctInfo`方法，将物理删除改为逻辑删除
- 删除前检查记录是否已被删除
- 设置`oth_msg1_tx`字段为"1"表示删除状态

```java
// 执行逻辑删除：设置oth_msg1_tx为"1"表示删除状态
boolean result = this.lambdaUpdate()
        .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
        .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
        .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
        .update();
```

#### 1.2 Mapper层修改
**文件**: `xm-xzp-impl/src/main/resources/mapper/TbZjjgAcctInfoMapper.xml`

**修改内容**:
1. **联想查询修改**: 只查询未删除的记录
```xml
<where>
    <!-- 只查询未删除的记录 -->
    (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    <if test="keyword != null and keyword != ''">
        AND (acct_nm LIKE CONCAT('%', #{keyword}, '%') OR cpab_acc_id LIKE CONCAT('%', #{keyword}, '%'))
    </if>
</where>
```

2. **分页查询修改**: 支持按状态查询
```xml
<where>
    <!-- 根据状态查询：如果指定了状态则按状态查询，否则只查询未删除的记录 -->
    <choose>
        <when test="queryVo.othMsg1Tx != null and queryVo.othMsg1Tx != ''">
            <if test="queryVo.othMsg1Tx == '1'">
                oth_msg1_tx = '1'
            </if>
            <if test="queryVo.othMsg1Tx == '0'">
                (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
            </if>
        </when>
        <otherwise>
            (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
        </otherwise>
    </choose>
    <!-- 其他查询条件... -->
</where>
```

#### 1.3 查询VO修改
**文件**: `xm-xzp-api/src/main/java/com/xm/xzp/model/vo/AcctInfoQueryVo.java`

**新增字段**:
```java
@ApiModelProperty(value = "状态：0-正常，1-已删除")
private String othMsg1Tx;
```

### 2. 前端修改

#### 2.1 配置文件修改
**文件**: `src/views/xzp/acctmgmt/infoData/acctMgmtOption.js`

**新增状态字段配置**:
```javascript
{
  label: '状态',
  prop: 'othMsg1Tx',
  type: 'select',
  width: 100,
  showColumn: true,
  addDisplay: false,
  editDisplay: false,
  viewDisplay: true,
  span: 12,
  search: true,
  searchLabelWidth: 200,
  searchSpan: 8,
  dicUrl: '/api/dict/type/xzp_acct_status',
  dicMethod: 'get',
  dicQuery: {},
  props: {
    label: 'dictLabel',
    value: 'dictValue'
  },
  placeholder: '请选择状态',
  clearable: true,
  formatter: (row, column, cellValue) => {
    if (cellValue === '1') return '已删除';
    if (cellValue === '0' || cellValue === null || cellValue === '') return '正常';
    return cellValue;
  }
}
```

#### 2.2 页面逻辑修改
**文件**: `src/views/xzp/acctmgmt/index.vue`

**修改内容**:
1. **单行删除逻辑**:
   - 检查记录是否已删除
   - 修改确认对话框提示信息，说明是逻辑删除

2. **批量删除逻辑**:
   - 过滤已删除的记录
   - 提示用户过滤的记录数量
   - 修改确认对话框提示信息

```javascript
// 单行删除（逻辑删除）
handleSingleDelete(row, index) {
  // 检查是否已经删除
  if (row.othMsg1Tx === '1') {
    this.$message.warning('该记录已被删除');
    return;
  }
  
  this.$confirm(`确定要删除监管账户"${row.cpabAccId}"的记录吗？\n注意：这是逻辑删除，记录将被标记为删除状态。`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 删除逻辑...
  });
}
```

### 3. 字典数据配置

#### 3.1 字典类型
- **字典类型ID**: `xzp_acct_status`
- **字典类型名称**: `账户状态`

#### 3.2 字典数据项
| 字典值 | 字典标签 | 说明 |
|--------|----------|------|
| 0 | 正常 | 账户正常状态 |
| 1 | 已删除 | 账户已删除状态 |

#### 3.3 初始化脚本
**文件**: `初始化账户状态字典数据.sql`

执行此脚本可以创建所需的字典类型和字典数据。

## 功能特性

### 1. 逻辑删除
- 删除操作不会物理删除数据库记录
- 通过`oth_msg1_tx`字段标记删除状态
- 保留数据完整性，支持数据恢复

### 2. 状态查询
- 新增状态查询下拉框
- 支持按状态筛选：正常、已删除
- 默认只显示正常状态的记录

### 3. 用户体验优化
- 删除确认对话框明确说明是逻辑删除
- 批量删除时自动过滤已删除记录
- 状态列显示直观的状态标签

### 4. 数据安全
- 防止重复删除已删除的记录
- 查询时默认排除已删除记录
- 保持数据一致性

## 使用说明

### 1. 查询功能
- 默认查询显示所有正常状态的记录
- 可通过状态下拉框选择查看已删除的记录
- 支持其他查询条件与状态条件组合使用

### 2. 删除功能
- 单行删除：点击操作列的删除按钮
- 批量删除：选择多条记录后点击批量删除按钮
- 删除后记录状态变为"已删除"，不会从列表中消失（需要刷新或重新查询）

### 3. 状态显示
- 正常记录：状态列显示"正常"
- 已删除记录：状态列显示"已删除"
- 已删除记录的删除按钮会提示"该记录已被删除"

## 注意事项

1. **数据库字段**: 确保`tb_zjjg_acct_info`表的`oth_msg1_tx`字段存在且类型为VARCHAR
2. **字典配置**: 需要先执行字典初始化脚本创建字典数据
3. **权限控制**: 删除功能仍受原有权限控制约束
4. **数据恢复**: 如需恢复已删除记录，可直接修改数据库将`oth_msg1_tx`设为NULL或'0'

## 测试建议

1. **功能测试**:
   - 测试单行删除功能
   - 测试批量删除功能
   - 测试状态查询功能
   - 测试已删除记录的删除按钮行为

2. **边界测试**:
   - 测试删除已删除的记录
   - 测试批量删除时包含已删除记录的情况
   - 测试状态字段为空或异常值的情况

3. **性能测试**:
   - 测试大量数据时的查询性能
   - 测试逻辑删除对查询性能的影响
