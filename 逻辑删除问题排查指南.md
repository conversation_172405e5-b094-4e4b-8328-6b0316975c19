# 逻辑删除问题排查指南

## 当前问题

1. **状态查询不生效**：选择状态"1"时，查询结果没有正确过滤
2. **修改失败**：提示"修改账户信息失败，可能是记录不存在"
3. **删除失败**：提示"删除账户信息失败，可能是记录不存在"

## 排查步骤

### 1. 检查数据库数据状态

访问调试接口检查数据分布：
```
GET /api/debug/checkDataStatus
```

这个接口会返回：
- 总记录数
- `oth_msg1_tx`字段的值分布（NULL、EMPTY、ZERO、ONE、OTHER）
- 前5条记录的详细信息

### 2. 测试状态查询逻辑

访问调试接口测试查询：
```
GET /api/debug/testStatusQuery
GET /api/debug/testStatusQuery?status=1
GET /api/debug/testStatusQuery?status=0
```

这个接口会返回：
- 不同查询条件的记录数量
- 具体的查询结果

### 3. 创建测试数据

如果没有已删除状态的数据，可以创建：
```
POST /api/debug/createTestData
```

### 4. 手动SQL验证

执行`调试SQL脚本.sql`中的SQL语句，验证：
- 数据状态分布
- 查询条件逻辑
- 更新操作结果

## 可能的问题原因

### 1. 数据库字段值问题

**可能情况**：
- `oth_msg1_tx`字段可能是空字符串`''`而不是`NULL`
- 字段可能有其他意外的值（如空格、特殊字符）

**解决方案**：
```sql
-- 统一处理空字符串为NULL
UPDATE public.tb_zjjg_acct_info 
SET oth_msg1_tx = NULL 
WHERE oth_msg1_tx = '' OR oth_msg1_tx = ' ';
```

### 2. MyBatis条件判断问题

**可能情况**：
- `ne()`方法不会匹配NULL值
- 条件组合逻辑有误

**解决方案**：
已在代码中修改为：
```java
.and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
        .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1"))
```

### 3. XML查询条件问题

**可能情况**：
- MyBatis XML中的条件判断逻辑错误
- 参数传递问题

**解决方案**：
已修改为嵌套的`<choose>`标签结构。

### 4. 前端参数传递问题

**可能情况**：
- 前端传递的参数格式不正确
- 参数名称不匹配

**检查方法**：
查看后端日志中的参数打印信息。

## 修复方案

### 方案1：数据清理（推荐）

```sql
-- 1. 清理异常数据
UPDATE public.tb_zjjg_acct_info 
SET oth_msg1_tx = NULL 
WHERE oth_msg1_tx = '' OR oth_msg1_tx = ' ' OR oth_msg1_tx = '0';

-- 2. 验证数据状态
SELECT 
    CASE 
        WHEN oth_msg1_tx IS NULL THEN 'NULL'
        WHEN oth_msg1_tx = '1' THEN 'DELETED'
        ELSE 'OTHER:' || oth_msg1_tx
    END as status,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
GROUP BY oth_msg1_tx;
```

### 方案2：代码逻辑调整

如果数据库中确实存在空字符串，可以调整代码逻辑：

```java
// 修改查询条件
.and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
        .or().eq(TbZjjgAcctInfo::getOthMsg1Tx, "")
        .or().eq(TbZjjgAcctInfo::getOthMsg1Tx, "0")
        .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1"))
```

### 方案3：XML条件优化

```xml
<choose>
    <when test="queryVo.othMsg1Tx != null and queryVo.othMsg1Tx != ''">
        <choose>
            <when test="queryVo.othMsg1Tx == '1'">
                oth_msg1_tx = '1'
            </when>
            <when test="queryVo.othMsg1Tx == '0'">
                (oth_msg1_tx IS NULL OR oth_msg1_tx = '' OR oth_msg1_tx = '0')
            </when>
        </choose>
    </when>
    <otherwise>
        (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    </otherwise>
</choose>
```

## 测试验证

### 1. 功能测试

1. **状态查询测试**：
   - 不选择状态：应显示所有正常记录
   - 选择"正常"：应显示所有正常记录
   - 选择"已删除"：应显示所有已删除记录

2. **删除功能测试**：
   - 删除正常记录：应成功，状态变为"已删除"
   - 删除已删除记录：应提示已删除

3. **修改功能测试**：
   - 修改正常记录：应成功
   - 修改已删除记录：应失败

### 2. 数据验证

```sql
-- 验证逻辑删除是否正常工作
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        ELSE '正常'
    END as status_desc
FROM public.tb_zjjg_acct_info
WHERE cpab_acc_id = '测试账户ID'
  AND acct_nm = '测试账户名';
```

## 注意事项

1. **备份数据**：在执行数据清理前，请备份相关数据
2. **逐步测试**：先使用调试接口测试，确认问题后再修复
3. **日志监控**：关注后端日志中的详细信息
4. **权限检查**：确保有访问调试接口的权限

## 联系支持

如果问题仍然存在，请提供：
1. 调试接口的返回结果
2. 后端日志中的错误信息
3. 前端网络请求的详细信息
4. 数据库查询的结果
