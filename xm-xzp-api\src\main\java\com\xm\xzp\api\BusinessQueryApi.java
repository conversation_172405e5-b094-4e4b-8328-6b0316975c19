package com.xm.xzp.api;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.vo.BusinessQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 业务查询API
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp/spf")
@Api(tags = "业务查询转发接口")
@Validated
public interface BusinessQueryApi {

    /**
     * 业务查询转发接口
     * 将前端请求转发到指定的targetUrl
     *
     * @param businessQueryVo 业务查询请求参数
     * @return 转发后的响应结果
     */
    @ApiOperation(value = "业务查询转发", notes = "将业务查询请求转发到目标URL")
    @PostMapping("/20002")
    RestResponse<Object> businessQuery(
            @ApiParam(value = "业务查询请求参数", required = true)
            @RequestBody @Valid BusinessQueryVo businessQueryVo
    );
}
