import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

//新中平-操作日志

// 新中平-日志查询;
export function xzpLogList (data) {
  let url = `/api/admin/xzp/log/xzpLogList?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}


//导出日志
export function exportLogs (params) {
  return request({
    url: '/api/admin/xzp/log/exportLogs',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 新中平-日志详情
export function viewLog (id) {
  return request({
    url: '/api/admin/xzp/log/logDetail/' + parseStrEmpty(id),
    method: 'get'
  })

}

//查询敏感信息
export function querySensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/log/sensitive/get/${id}/${sensitiveStatus}`,
    method: 'get',
  })
}


