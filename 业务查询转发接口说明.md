# 业务查询转发接口说明

## 接口概述

本接口用于将前端的业务查询请求转发到指定的目标URL，实现请求代理转发功能。主要用于前端xzp/acctquery页面的查询功能。

## 接口信息

- **接口路径**: `/api/xzp/spf/20002`
- **请求方式**: `POST`
- **功能描述**: 接收前端业务查询请求，转发到targetUrl指定的目标地址
- **返回格式**: `RestResponse<Object>`

## 请求参数

### 请求体参数 (BusinessQueryVo)

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| busikind | String | 是 | 业务类型 | "CHANGE" |
| tradecode | String | 是 | 交易代码 | "Feedbac" |
| merchid | String | 是 | 商户号 | "************" |
| opecd | String | 是 | 操作代码 | "101064" |
| empname | String | 是 | 员工姓名 | "张三" |
| empcode | String | 是 | 员工代码 | "001" |
| orgcode | String | 是 | 机构代码 | "001001" |
| orgdegree | String | 是 | 机构级别 | "2" |
| account | String | 是 | 账户 | "****************" |
| bgn_date | String | 是 | 开始日期 | "********" |
| end_date | String | 是 | 结束日期 | "********" |
| targetUrl | String | 是 | 目标URL | "http://example.com/api/query" |
| action | String | 是 | 操作类型 | "query" |

## 请求示例

```json
{
  "busikind": "CHANGE",
  "tradecode": "Feedbac",
  "merchid": "************",
  "opecd": "101064",
  "empname": "张三",
  "empcode": "001",
  "orgcode": "001001",
  "orgdegree": "2",
  "account": "****************",
  "bgn_date": "********",
  "end_date": "********",
  "targetUrl": "http://example.com/api/query",
  "action": "query"
}
```

## 响应示例

### 成功响应
```json
{
  "code": "0",
  "message": "操作成功",
  "data": {
    // 目标服务返回的实际数据
    "result": "success",
    "records": [...]
  },
  "success": true
}
```

### 失败响应
```json
{
  "code": "500",
  "message": "业务查询转发失败: 连接超时",
  "data": null,
  "success": false
}
```

## 转发逻辑

1. 接收前端请求参数
2. 验证必填参数（特别是targetUrl）
3. 构建转发请求体（排除targetUrl字段）
4. 设置HTTP请求头（Content-Type: application/json）
5. 使用RestTemplate发送POST请求到目标URL
6. 返回目标服务的响应结果

## 技术实现

### 文件结构

1. **请求VO**: `BusinessQueryVo.java` - 定义请求参数结构
2. **API接口**: `BusinessQueryApi.java` - 定义接口规范
3. **Service接口**: `IBusinessQueryService.java` - 业务逻辑接口
4. **Service实现**: `BusinessQueryServiceImpl.java` - 转发逻辑实现
5. **Controller**: `BusinessQueryController.java` - 控制器实现
6. **配置类**: `RestTemplateConfig.java` - HTTP客户端配置

### 关键特性

- 使用RestTemplate进行HTTP转发
- 支持连接超时和读取超时配置（30秒连接，60秒读取）
- 完整的异常处理和日志记录
- 支持Swagger API文档自动生成
- 集成审计日志功能（@PMCTLLog注解）

## 配置说明

### RestTemplate配置
- 连接超时：30秒
- 读取超时：60秒
- 请求头：application/json

### 日志配置
- 请求日志：记录转发的目标URL和请求参数
- 响应日志：记录转发结果和响应状态
- 错误日志：记录转发失败的详细错误信息

## 注意事项

1. 确保targetUrl参数有效且可访问
2. 目标服务需要支持POST请求和JSON格式
3. 转发请求会移除targetUrl字段，只传递业务参数
4. 建议在生产环境中配置适当的超时时间
5. 可根据实际需求调整请求头和参数格式

## 前端集成

前端通过以下方式调用：

```javascript
import { businessQuery } from '@/api/acctquery';

const requestData = {
  busikind: 'CHANGE',
  tradecode: 'Feedbac',
  merchid: '************',
  opecd: '101064',
  empname: this.$store.state.user.name,
  empcode: this.$store.state.user.empCode,
  orgcode: this.$store.state.user.orgCode,
  orgdegree: this.$store.state.user.orgDegree,
  account: this.search.account,
  bgn_date: this.search.bgnDate,
  end_date: this.search.endDate,
  targetUrl: targetUrl, // 从数据字典获取
  action: 'query'
};

const response = await businessQuery(requestData);
```
