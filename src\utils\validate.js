import Cookies from 'js-cookie'


/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal (path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername (str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL (url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase (str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase (str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets (str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail (email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString (str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray (arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}
export default {
  // 是否为空
  blankSpace : (rule, value, callback) => {
    value = value.replace(/\s*/g,"")
    if (!value) {
      return callback(new Error('输入值不能为空'));
    }
    callback()
  },
  //非负整数
  isNumber: (rule, value, callback, source, options) => {
    var errors = []
    if (value && !/^\d+$/.test(value)) {
      callback(new Error('请输入数字值'))
    }
    callback(errors)
  },

  telNumber: (rule, value, callback, source, options) => {
    const telNumber = Cookies.get('PhoneNumber')
    if(telNumber && telNumber == value){
      callback()
    }
    // if (value && !/^((1[3-9][0-9])\d{8})|(0\d{2}-\d{8})|(0\d{3}-\d{7})$/.test(value)) {
      if (value && !/^[1][3-9][0-9]{9}$/.test(value)) {
      callback(new Error('手机号格式不正确'))
    }
    callback()
  },
  email: (rule, value, callback, source, options) => {
    const email = Cookies.get('email')
    if(email && email == value){
      callback()
    }
    const reg = /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(\.[0-9A-Za-z]+)+$/
    if (value && !reg.test(value)) {
      callback(new Error('邮箱格式不正确'))
    }
    callback()
  },
  //密码必须包含字母、数字、特殊字符,6-20位
  enhancedPWD: (rule, value, callback, source, options) => {
    const reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,20}$/
    if (!reg.test(value)) {
      callback(new Error('密码必须包含字母、数字、特殊字符,6-20位'))
    }
    callback()
  },
  // 身份证
  checkIdentity: (rule, value, callback, ) => {
    const idCard = Cookies.get('IdCard')
    if(idCard && idCard == value){
      callback()
    }
    const reg = /^[1-9]{1}[0-9]{14}$|^[1-9]{1}[0-9]{16}([0-9]|[xX])$/
    if (!reg.test(value)) {
      callback(new Error('身份证格式不正确'))
    }
    callback()
  },
  //roleid规则
  roleIdValidate: (rule, value, callback, source, options) => {
    const reg = /^\w+$/
    if (value &&!reg.test(value)) {
      callback(new Error('只能由数字,英文字母或下划线组成'))
    }
    callback()
  },
  //userid规则
  userIdValidate: (rule, value, callback, source, options) => {
    const reg = /^[\u4E00-\u9FA5a-zA-Z0-9_]+$/
    if (value &&!reg.test(value)) {
      callback(new Error('只能由中文,数字,英文字母或下划线组成'))
    }
    callback()
  },
}
