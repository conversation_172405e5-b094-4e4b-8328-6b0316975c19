// 新中平操作日志信息列表、弹框项
import validate from '@/utils/validate';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  viewTitle: '日志信息查询',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menuWidth: 100,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: true, //是否获取列值
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '日志类型',
      prop: 'applyType',
      slot: true,
      // width: 100,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },

    {
      label: '操作步骤类型',
      prop: 'stepId',
      slot: true,
      width: 120,
      showColumn: false,
      addDisplay: true,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 7, //右边长度
    },
    {
      label: '操作步骤描述',
      prop: 'stepName',
      slot: true,
      width: 240,
      showColumn: false,
      addDisplay: true,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 7, //右边长度
    },
    {
      label: '操作人',
      prop: 'createBy',
      slot: true,
      // width: 100,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
    },
    {
      label: '操作时间',
      prop: 'createTime',
      width: 150,
      searchslot: true,
      search: true,
      viewDisplay: false,
      //查询设置：
      searchLabelWidth: 120, //左边长度
      searchSpan: 7, //右边长度
    },

    {
      label: '处理人',
      prop: 'dealUser',
      hide: true,
      slot: true,
      // width: 100,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '处理内容',
      prop: 'opinion',
      slot: true,
      width: 400,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '处理结果',
      prop: 'dealRes',
      slot: true,
      // width: 120,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '关联表',
      prop: 'flowId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '关联表ID',
      prop: 'speReleId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },

    /*  {
        label: '预留字段',
        prop: 'reserve',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段1',
        prop: 'reserveA',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段2',
        prop: 'reserveB',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段3',
        prop: 'reserveC',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段4',
        prop: 'reserveD',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      }, */
  ],
};
