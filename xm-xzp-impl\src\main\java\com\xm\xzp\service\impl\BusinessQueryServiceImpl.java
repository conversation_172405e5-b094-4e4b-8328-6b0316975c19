package com.xm.xzp.service.impl;

import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务查询服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessQueryServiceImpl implements IBusinessQueryService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Object executeBusinessQuery(BusinessQueryVo businessQueryVo) {
        log.info("开始执行业务查询转发，目标URL: {}", businessQueryVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(businessQueryVo);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<Object> response = restTemplate.exchange(
                    businessQueryVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    Object.class
            );
            
            log.info("业务查询转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("业务查询转发失败，目标URL: {}, 错误信息: {}", businessQueryVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "业务查询转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    /**
     * 构建转发请求的请求体
     */
    private Map<String, Object> buildRequestBody(BusinessQueryVo businessQueryVo) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("busikind", businessQueryVo.getBusikind());
        requestBody.put("tradecode", businessQueryVo.getTradecode());
        requestBody.put("merchid", businessQueryVo.getMerchid());
        requestBody.put("opecd", businessQueryVo.getOpecd());
        requestBody.put("empname", businessQueryVo.getEmpname());
        requestBody.put("empcode", businessQueryVo.getEmpcode());
        requestBody.put("orgcode", businessQueryVo.getOrgcode());
        requestBody.put("orgdegree", businessQueryVo.getOrgdegree());
        requestBody.put("account", businessQueryVo.getAccount());
        requestBody.put("bgn_date", businessQueryVo.getBgn_date());
        requestBody.put("end_date", businessQueryVo.getEnd_date());
        requestBody.put("action", businessQueryVo.getAction());
        
        return requestBody;
    }
}
