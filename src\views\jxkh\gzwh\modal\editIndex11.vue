<template>
    <el-dialog
        :destroy-on-close="true"
        title="绩效考核"
        :visible.sync="dialogVisible"
        width="90%"
    >
        <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
                <el-input
                    v-model="search.empName"
                    placeholder="员工姓名（输入关键字模糊查询）"
                ></el-input>
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="searchData">查 询</el-button>
                <el-button @click="resetQuery">重 置</el-button>

                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  :size="size"
                  plain
                  @click.stop="handleAdd()"
                >新增人员</el-button>
            </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tableData" style="width: 100%">
            <el-table-column
                v-if="tableData.length > 0"
                width="120"
                key="header.prop"
                prop="empName"
                label="姓名"
            ></el-table-column>

            <el-table-column
                v-for="(header, index) in tableHeaders"
                :key="header.prop"
                :prop="header.prop"
                :label="header.label"
            >
                <template slot-scope="scope">
                    <el-input-number v-if="!isDetail"
                        controls-position="right"
                        v-model="
                            listParams[
                                tableHeaders.length * scope.$index + index
                            ].salary
                        "
                        @change="handleEdit(scope.$index, scope.row)"
                    ></el-input-number>
                    <span v-else>{{
                            listParams[
                                tableHeaders.length * scope.$index + index
                            ].salary }}</span>
                </template>
            </el-table-column>
        </el-table>

        <span   slot="footer" class="dialog-footer">
            <el-pagination
                background
                style="margin-bottom: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total,sizes, prev, pager, next"
                :total="total"
            ></el-pagination>

            <el-button v-if="!isDetail" @click="dialogVisible = false">取 消</el-button>
            <el-button v-if="!isDetail" type="primary" @click="sureBtn">保 存</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { list, batchAdd } from '@/api/jxkh/jxkh';

export default {
    data() {
        return {
            search: {
                empName: '',
            },

            tableData: [],
            tableHeaders: [],
            dialogVisible: false,
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            listParams: [],
            total: 0,
            isDetail:false
        };
    },
    methods: {
        onLoad(page) {
            this.getList(this.search);
        },
        handleEdit(index, row) {
            // 编辑操作
        },
        handleDelete(index, row) {
            // 删除操作
        },
        /** 分页查询列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await list(params);

            if (response.data.data != undefined) {
                this.total = response.data.data.total;
                this.tableHeaders = response.data.head;
                this.tableData = response.data.data.list;
                let allData = response.data.allData;
                this.listParams = [];
                this.tableData.forEach(item => {
                    this.tableHeaders.forEach(ss => {
                        let a = allData.filter(
                            all => all.empId == item.empId && all.bh == ss.prop
                        )[0];
                        this.listParams.push({
                            empId: item.empId,
                            bh: ss.prop,
                            salary: a.salary,
                            batchId: params.batchId,
                            id: a.id,
                        });
                    });
                });
            } else {
                this.tableData = [];
                this.tableHeaders = [];
            }

            this.loading = false;
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.page.currentPage = 1;
            this.getList(this.search);
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getList(this.search);
        },
        openModal(row, index,isDetail) {
            this.search.batchId = row.id;
            this.dialogVisible = true;

            this.isDetail = isDetail ;

            this.$nextTick(() => {
                this.resetQuery();
            });
        },
        handleEdit(index, row) {
            // 处理编辑逻辑，例如更新数据库等
            console.log(index, row.index);
        },
        async sureBtn() {
            // this.dialogVisible = false;
            this.loading = true;
            const res = await batchAdd(this.listParams);
            if (res.code == 0) {
                this.$message.success(res.message);
            } else {
                this.$message.error(res.message);
            }
            this.loading = false;
        },
        searchData() {
            this.getList(this.search);
        },
        resetQuery() {
            //重置
            this.search.empName = '';
            this.page = {
                pageSize: 10,
                currentPage: 1,
            };
            this.getList(this.search);
        },
      /** 新增按钮操作 */
      handleAdd() {
        let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          let r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
        (this.formParent = {
          id: uuid
        }),
          this.$refs.crud.rowAdd();
      },
    },
};
</script>
