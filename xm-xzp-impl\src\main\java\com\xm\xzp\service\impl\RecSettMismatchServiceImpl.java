package com.xm.xzp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.exception.BizException;
import com.xm.xzp.mapper.RecSettMismatchMapper;
import com.xm.xzp.model.entity.DictEntry;
import com.xm.xzp.model.entity.RecSettMismatch;
import com.xm.xzp.model.query.DictEntryQuery;
import com.xm.xzp.model.vo.RecSettMismatchVo;
import com.xm.xzp.service.IDictEntryService;
import com.xm.xzp.service.IRecSettMismatchService;
import com.xm.xzp.service.IXzpOpLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class RecSettMismatchServiceImpl extends ServiceImpl<RecSettMismatchMapper, RecSettMismatch> implements IRecSettMismatchService {

    @Resource
    private RecSettMismatchMapper recSettMismatchMapper;

    @Resource
    private IXzpOpLogService xzpOpLogService;

    @Resource
    private IDictEntryService dictEntryService;
    /**
     * 查询批量交易控制列表
     *
     * @param recSettMismatch 查询条件
     * @return 分页信息
     */
    @Override
    @DS("datasource2")
    public PageInfo<RecSettMismatch> recSettMismatchList(RecSettMismatchVo recSettMismatch, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<RecSettMismatch> list = this.list(recSettMismatchQueryWrapper(recSettMismatch));
        return new PageInfo<>(list);
    }

    /**
     * 构建查询条件
     *
     * @param recSettMismatch 查询条件对象
     * @return QueryWrapper
     */
    private QueryWrapper<RecSettMismatch> recSettMismatchQueryWrapper(RecSettMismatchVo recSettMismatch) {
        QueryWrapper<RecSettMismatch> queryWrapper = new QueryWrapper<>();
        if (recSettMismatch != null) {
            LambdaQueryWrapper<RecSettMismatch> lambdaQueryWrapper = queryWrapper.lambda();
            //不符类型
            if ( StringUtils.isNotBlank(recSettMismatch.getRecordType()) ) {
                lambdaQueryWrapper.eq(RecSettMismatch::getRecordType, recSettMismatch.getRecordType());
            }
            //银联清算日期
            if (StringUtils.isNotBlank(recSettMismatch.getMerchDt())) {
                lambdaQueryWrapper.eq(RecSettMismatch::getMerchDt, recSettMismatch.getMerchDt());
            }
            //交易状态
            if (StringUtils.isNotBlank(recSettMismatch.getRecordSta())) {
                lambdaQueryWrapper.eq(RecSettMismatch::getRecordSta, recSettMismatch.getRecordSta());
            }

        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public RecSettMismatch getOne(Wrapper<RecSettMismatch> queryWrapper) {
        return super.getOne(queryWrapper);
    }

    @Override
    @DS("datasource2")
    public boolean editRecSettMismatch(RecSettMismatch recSettMismatch) {
        // 参数校验 交易日期  业务代码 交易代号 委托单位代码 用户号 委托方流水号
        if (StringUtils.isBlank(recSettMismatch.getTranDt())
                || StringUtils.isBlank(recSettMismatch.getOpeCd())
                || StringUtils.isBlank(recSettMismatch.getTranCd())
                || StringUtils.isBlank(recSettMismatch.getMerchId())
                || StringUtils.isBlank(recSettMismatch.getUserId())
        ){ //merch_id
            throw new BizException("必填参数不能为空");
        }

        // 检查记录是否存在
        LambdaQueryWrapper<RecSettMismatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecSettMismatch::getTranDt, recSettMismatch.getTranDt())
                .eq(RecSettMismatch::getOpeCd, recSettMismatch.getOpeCd())
                .eq(RecSettMismatch::getTranCd, recSettMismatch.getTranCd())
                .eq(RecSettMismatch::getMerchId, recSettMismatch.getMerchId())
                .eq(RecSettMismatch::getUserId, recSettMismatch.getUserId());

        RecSettMismatch existRecord = this.getOne(queryWrapper);
        if (existRecord == null) {
            throw new BizException("记录不存在");
        }

        try {
            return recSettMismatchMapper.updateRecSettMismatch(recSettMismatch) > 0;
        } catch (Exception e) {
            log.error("编辑批量交易失败", e);
            throw new BizException("编辑失败");
        }
    }


    @Override
    @DS("datasource2")
    public Workbook exportRecSettMismatch(RecSettMismatchVo recSettMismatchVo) {
        TemplateExportParams params = new TemplateExportParams("doc/xzp/对账清算不一致信息导出模板.xls");
        List<RecSettMismatch> recSettMismatchList =  this.list(recSettMismatchQueryWrapper(recSettMismatchVo));
        Map<String, Object> map = new HashMap<>(recSettMismatchList.size());
        DictEntryQuery dictEntryQuery =new DictEntryQuery();
        dictEntryQuery.setDictTypeId("xzp_record_type");
        List<DictEntry> recordTypeList  =dictEntryService.selectDictEntry(dictEntryQuery);
        //处理字典表数据
        if (!recSettMismatchList.isEmpty() && !recordTypeList.isEmpty()) {
            recSettMismatchList.forEach(recSettMismatch -> {
                DictEntry dictEntry = recordTypeList.stream().filter(dict -> dict.getDictId().equals(recSettMismatch.getRecordType())).findAny().orElse(null);
                if (dictEntry != null) {
                    recSettMismatch.setRecordType(dictEntry.getDictName());
                }
            });
        }
        DictEntryQuery dictEntryQuery1 =new DictEntryQuery();
        dictEntryQuery1.setDictTypeId("xzp_record_sta");
        List<DictEntry> recordStaList  =dictEntryService.selectDictEntry(dictEntryQuery1);
        //处理字典表数据
        if (!recSettMismatchList.isEmpty() && !recordStaList.isEmpty()) {
            recSettMismatchList.forEach(recSettMismatch -> {
                DictEntry dictEntry = recordStaList.stream().filter(dict -> dict.getDictId().equals(recSettMismatch.getRecordSta())).findAny().orElse(null);
                if (dictEntry != null) {
                    recSettMismatch.setRecordSta(dictEntry.getDictName());
                }
            });
        }
        map.put("recSettMismatchList", recSettMismatchList);
        //记录日志
        /*XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("导出");
        xzpOpLog.setStepName("导出对账清算不一致信息");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("导出对账清算不一致信息成功");

        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[exportRecSettMismatch]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("recSettMismatchList");
        xzpOpLog.setFlowId("tb_3502_xmykt_dz_result");
        xzpOpLogService.addLog(xzpOpLog);*/
        return ExcelExportUtil.exportExcel(params, map);
    }
}
