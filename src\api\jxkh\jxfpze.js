import request from '@/utils/request'

/**
 * 2022/3/2 liyang
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query
 */
/**
 * 2022/3/2 liyang
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query
 */
export function listSalarySub (data) {
  const { name = "",category="",state="", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/jxkh/kmwh/getPageSalarySub?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {

      name,
      category,
      state

    }
  })
}


export function listJxfpze (data) {
  const { deptId = "",type="",year="",yearQuar="",yearMonth="", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/jxkh/kmwh/getPageJxkhJxfpze?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {

      deptId,
      type,
      year,
      yearQuar,
      yearMonth
    }
  })
}



// 新增科目
export function addJxfpze (data) {
  return request({
    url: '/api/admin/jxkh/kmwh/addJxkhJxfpze',
    method: 'post',
    data: data
  })
}

// 修改科目
export function updateJxfpze (data) {
  return request({
    url: '/api/admin/jxkh/kmwh/editJxkhJxfpze',
    method: 'post',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus (roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除科目
export function delJxfpze (id) {
  return request({
    url: '/api/admin/jxkh/kmwh/removeJxkhJxfpze/' + id,
    method: 'post'
  })
}

//下载模板
export function downloadExample () {
  /*var xhr = new XMLHttpRequest();
  xhr.open('POST', process.env.VUE_APP_BASE_API+'/api/admin/jxkh/kmwh/exportJxkhJxfpze', true);
  xhr.responseType = 'blob';

  xhr.onload = function() {
    if (this.status === 200) {
      // 正常情况处理
      var blob = this.response;
      // ... 使用 blob 对象的代码
    }
  };

  xhr.onerror = function() {
    // 异常处理
    console.error('请求出错');
  };

  xhr.send();*/
  return request({
    url: '/api/admin/jxkh/kmwh/exportJxkhJxfpze',
    method: 'post',
    responseType: 'blob',
  });
}

//导入模板
export function importEx(data) {
  return request({
    url: '/api/admin/jxkh/kmwh/importEx',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

