import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

// 存量卡-明细列表查询
export function listData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/existCards/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}

// 存量卡-总控列表查询
export function ctrlListData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/existCards/ctrlList?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}

// 存量卡-数据推送-明细表
export function pushData(data) {
    return request({
        url: '/api/admin/jyt/existCards/pushExistingCards',
        method: 'post',
        data: data,
    });
}

// 单条数据
export function sendExistCard (id) {
  return request({
    url: '/api/admin/jyt/existCards/sendExistCard' + id,
    method: 'post',
  });
}

// 多条数据
export function sendExistCards (ids) {
  return request({
    url: '/api/admin/jyt/existCards/sendExistCards',
    method: 'post',
    data: ids
  });
}

// 存量卡-数据推送-总控表
export function pushExistCardsBySerialno(data) {
    return request({
        url: '/api/admin/jyt/existCards/pushExistCardsBySerialno',
        method: 'post',
        data: data,
    });
}

// 存量卡-状态查询-总控表
export function getStatus(data) {
    return request({
        url: '/api/admin/jyt/existCards/exeStatus',
        method: 'post',
        data: data,
    });
}

// 存量卡-状态查询-总控表
export function getStatusByOne (serialno) {
  return request({
    url: '/api/admin/jyt/existCards/exeStatus/' + parseStrEmpty(serialno),
    method: 'get'
  });
}

// 存量卡-结果获取-总控表
export function getResult(data) {
    return request({
        url: '/api/admin/jyt/existCards/getResult',
        method: 'post',
        data: data,
    });
}


// 存量卡-详情
export function viewDtl (id) {
  return request({
    url: '/api/admin/jyt/existCards/detail/' + parseStrEmpty(id),
    method: 'get'
  });
}

//查询敏感信息
export function querySensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/jyt/existCards/sensitive/getDtl/${id}/${sensitiveStatus}`,
    method: 'get',
  });
}

//查询敏感信息
export function queryCtrlSensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/jyt/existCards/sensitive/getCtrl/${id}/${sensitiveStatus}`,
    method: 'get',
  });
}
