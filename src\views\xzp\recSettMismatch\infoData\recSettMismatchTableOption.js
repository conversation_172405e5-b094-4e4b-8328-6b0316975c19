// 对账/清算不一致表信息列表、弹框项
export default {
  index: true,
  indexLabel: '序号',
  rowKey: 'batchId',
  reserveSelection: true,
  selection: false,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  viewTitle: '对账/清算不一致表',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menu: true,
  column: [
    {label: '业务代码',prop: 'opeCd',editDisabled: true, width:120},
    { label: '委托单位', prop: 'merchId', editDisabled: true },
    { label: '交易日期', prop: 'tranDt',editDisabled: true,width:120 },
    { label: '银联清算日期', prop: 'merchDt',editDisabled: true,width:120 },
    { label: '邮储日期', prop: 'hostDt',editDisabled: true,width:120},
    { label: '子业务', prop: 'typeCd',editDisabled: true },
    { label: '用户号', prop: 'userId',editDisabled: true },
    { label: '中心流水号', prop: 'tranSq',editDisabled: true },
    { label: '卡号/账号', prop: 'accCardId',editDisabled: true },
    { label: '交易代码', prop: 'tranCd',editDisabled: true },
    { label: '交易金额', prop: 'tranAmt',editDisabled: true },
    { label: '交易状态',prop: 'recordSta',type: 'select',width:200,dicData: [] }, // 将通过updateDictData方法动态更新
    { label: '记录类型',prop: 'recordType',type: 'select',width:200,dicData: [],editDisabled: true}, // 将通过updateDictData方法动态更新
  ]
}; 