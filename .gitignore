### ignore files or directories ###
target/
logs/
**/classes/
.mvn
~$*
*~
*.log

### Editor directories and files ###
.idea
.vscode
.classpath
.project
.settings
.apt_generated
.factorypath
.springBeans
.sts4-cache
.DS_Store
.checkstyle
*.suo
*.ntvs*
*.njsproj
*.sln
*.iml
*.iws
*.ipr

### NetBeans ###
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

### web ###
node_modules/
/dist/
/output/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/test/unit/coverage/
/test/e2e/reports/
/src/assets/test/
selenium-debug.log
references_temp/

.DS_Store
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/*.log

tests/**/coverage/
tests/e2e/reports
selenium-debug.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.local

package-lock.json
yarn.lock