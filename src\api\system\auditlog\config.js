import request from '@/utils/request'

/**
 * 2022/4/7
 * @param {*systemCode: 系统编号,message: 交易名称,requestURI:请求地址,status:状态,pageNum：当前页数,pageSize：每页几条} data 
 */
// 分页查询审计日志配置列表
export function listConfig(data) {
  const {systemCode = '', message = '',requestURI = '',status = '',  currentPage = 1, pageSize = 10 } = data || {}

  return request({
    url: `/api/audit/logs/configs/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {systemCode, message, requestURI, status}
  })
}

// 新增一条审计日志配置
export function addConfig(data) {
  return request({
    url: '/api/audit/logs/configs/actions/add',
    method: 'post',
    data: data
  })
}

// 根据id查询一条审计日志配置接口
export function getConfigId(Id) {
  return request({
    url: '/api/audit/logs/configs/actions/get/' + Id,
    method: 'get'
  })
}

// 启用/停用审计日志配置接口
export function onOffConfig(data) {
  return request({
    url: '/api/audit/logs/configs/actions/on-off',
    method: 'post',
    data: data
  })
}

// 更新一条审计日志配置接口
export function updateConfig(data) {
  return request({
    url: '/api/audit/logs/configs/actions/edit',
    method: 'post',
    data: data
  })
}

// 删除一条审计日志配置接口
export function delConfig(Id) {
  return request({
    url: '/api/audit/logs/configs/actions/remove/' + Id,
    method: 'post'
  })
}
// 批量删除审计日志配置接口
export function delConfigs(Ids) {
  return request({
    url: '/api/audit/logs/configs/actions/remove',
    method: 'post',
    data:Ids
  })
}
//导出审计日志配置信息接口
export function exportConfig(data) {
  return request({
    url: '/api/audit/logs/configs/actions/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//下载审计日志配置信息导入模板
export function downloadExample(data) {
  return request({
    url: '/api/audit/logs/configs/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob'
  })
}
//导入审计日志信息接口
export function importConfig(data) {
  return request({
    url: '/api/audit/logs/configs/actions/import',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete'
  })
}
