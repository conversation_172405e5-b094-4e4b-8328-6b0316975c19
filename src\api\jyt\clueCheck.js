import request from '@/utils/request';

// 线索交互核查-列表查询
export function listData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/clueCheck/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}

// 线索交互核查-报送
export function pushData(data) {
    return request({
        url: '/api/admin/jyt/clueCheck/push',
        method: 'post',
        data: data,
    });
}


// 线索交互核查-结果获取
export function getResult(data) {
    return request({
        url: '/api/admin/jyt/clueCheck/getResult',
        method: 'post',
        data: data,
    });
}

// 线索交互核查-结果获取
export function viewResult(data) {
    return request({
        url: '/api/admin/jyt/clueCheck/viewResult',
        method: 'post',
        data: data,
    });
}
