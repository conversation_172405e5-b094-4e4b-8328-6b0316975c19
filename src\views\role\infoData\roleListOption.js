import validate from "@/utils/validate"
export default {
  index:true,
  indexLabel: '序号',
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan:4,
  searchMenuPosition: 'left',
  addTitle: '新增角色',
  editTitle: '修改角色',
  saveBtnText: '确定',
  editBtnText: '修改',
  updateBtnText: '确定',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  labelWidth: 100,
  columnBtn:false,
  column: [
    {
      label: '角色编号',
      prop: 'roleId',
      search: true,
      editDisabled: true,
      slot: true,
      rules: [{
        required: true,
        message: "角色编号不能为空",
        trigger: "blur"
      },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
      {
        validator: validate.roleIdValidate, trigger: 'blur'
      }
    ]
    },
    {
      label: '角色名称',
      prop: 'roleName',
      search: true,
      slot: true,
      rules: [{
        required: true,
        message: "角色名称不能为空",
        trigger: "blur"
      },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
      {validator: validate.blankSpace, trigger: 'blur'}]
    },
    {
      label: '角色状态',
      prop: 'status',
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      rules: [{
        required: true,
        message: "请选择状态",
        trigger: "blur,change"
      }]
    },
    {
      label: '角色类型',
      prop: 'roleType',
      editDisplay: false,
      addDisplay: false,
      headerslot:true,
    },
    {
      label: '创建人',
      prop: 'createUser',
      editDisplay: false,
      addDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      editDisplay: false,
      addDisplay: false,
    }
  ],
  // group: [{
  //   column: [
  //     {
  //       label: '角色名称',
  //       prop: 'roleName',
  //       rules: [{
  //         required: true,
  //         message: "角色名称不能为空",
  //         trigger: "blur"
  //       }]
  //     },
  //     {
  //       label: '权限字符',
  //       prop: 'roleKey',
  //       search: true,
  //       labelTip: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)",
  //       rules: [{
  //         required: true,
  //         message: "权限字符不能为空",
  //         trigger: "blur"
  //       }]
  //     },
  //     {
  //       label: '角色顺序',
  //       prop: 'roleSort',
  //       type: "number",
  //       minRows: 0,
  //       value: 0,
  //       rules: [
  //         {
  //           required: true,
  //           message: "角色顺序不能为空",
  //           trigger: "blur"
  //         }
  //       ]
  //     },
  //     {
  //       label: '状态',
  //       prop: 'status',
  //       type: 'radio',
  //       search: true,
  //       slot: true,
  //       searchslot: true,
  //       value: "0",
  //       dicData: [{
  //         label: '正常',
  //         value: "0"
  //       }, {
  //         label: '停用',
  //         value: "1"
  //       }]
  //     }, {
  //       label: '菜单权限',
  //       prop: 'menuPermission',
  //       span: 24,
  //       formslot: true
  //     }, {
  //       label: '备注',
  //       prop: 'remark',
  //       type: 'textarea',
  //       placeholder: '请输入内容',
  //       span: 24
  //     }
  //   ]
  // }]
}