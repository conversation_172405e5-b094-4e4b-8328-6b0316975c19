import auth from '@/plugins/auth'
import router, { constantRoutes, dynamicRoutes } from '@/router'
import { getRouters } from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import isArray from "yo-utils/isArray";
import { systemType } from '@/utils/constant'
const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = constantRoutes.concat(routes)
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes)
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [{
        path: 'index',
        meta: { title: '统计报表', icon: 'dashboard' }
      }]
      state.topbarRouters = routes.concat(index);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes ({ commit }) {
      return new Promise(resolve => {
        // 添加系统编号参数
        let params = systemType
        getRouters(params).then(res => {
          if (res && res.data && isArray(res.data)) {
            let routesArr = []
            // 1.路由菜单数据处理：[{'yoaf': [T]}, {'XXX', [T]}] => [T]
            res.data.forEach((mapData, index) => {
              Object.values(mapData).forEach((item, indexItem) => {
                routesArr = routesArr.concat(item)
              })
            })
            // sidebarRoutes与rewriteRoutes 共享同一路由数据
            // const sidebarRoutes = filterAsyncRouter(JSON.parse(JSON.stringify(routesArr)))
            const rewriteRoutes = filterAsyncRouter(routesArr, false, true)
            // 动态路由全部由后端接口加载
            // const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
            rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })

            commit('SET_ROUTES', rewriteRoutes)
            commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(rewriteRoutes))
            commit('SET_DEFAULT_ROUTES', rewriteRoutes)
            commit('SET_TOPBAR_ROUTES', rewriteRoutes)
            resolve(rewriteRoutes)

          } else {
            // 路由菜单数据异常拼装默认路由
            const rewriteRoutes = [{ path: '*', redirect: '/404', hidden: true }]
            commit('SET_ROUTES', rewriteRoutes)
            commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(rewriteRoutes))
            commit('SET_DEFAULT_ROUTES', rewriteRoutes)
            commit('SET_TOPBAR_ROUTES', rewriteRoutes)
            resolve(rewriteRoutes)
          }
        })
      })
    }
  }
}

/**
 * 遍历后台传来的路由字符串，转换为组件对象
 *
 * @param asyncRouterMap 动态路由数据集合
 * @param lastRouter 是否拼接上级路由地址
 * @param type
 * @returns {*}
 */
function filterAsyncRouter (asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    // 一级菜单目录转换'component'属性为Layout组件
    if (route.menuType && route.menuType == '1') {
      route.component = Layout;
    }
    // 处理子目录/子菜单
    if (route.children != null && route.children && route.children.length > 0) {
      route.children = filterChildren(route.children)
    } else {
      // 删除'children'与'redirect'属性
      delete route['children']
      delete route['redirect']
    }

    return true
  })
}

/**
 * 遍历子路由
 *
 * @param childrenMap
 * @param lastRouter
 * @returns {[]}
 */
function filterChildren (childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    // 转换'component'属性为对应组件
    if (el.menuType) {
      // 目录
      if (el.menuType == '1') {
        el.component = ParentView;
      } else if (el.menuType == '2'){
        // 路由菜单
        el.component = loadView(el.component)
      } else if (el.menuType == '3') {
        // 外链菜单
        el.component = Layout;
      }
    }

    if (el.children != null && el.children && el.children.length) {
      el.children = filterChildren(el.children, false)
    }

    children = children.concat(el)
  })
  return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes (routes) {
  const res = []
  routes.forEach(route => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route)
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route)
      }
    }
  })
  return res
}

export const loadView = (view) => {
  if (process.env.NODE_ENV === 'development') {
    return (resolve) => require([`@/views/${view}`], resolve)
  } else {
    // 使用 import 实现生产环境的路由懒加载
    return () => import(`@/views/${view}`)
  }
}

export default permission
