package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资金监管账户信息服务接口
 * <AUTHOR>
 */
@Component
public interface ITbZjjgAcctInfoService extends IService<TbZjjgAcctInfo> {

    /**
     * 根据关键字模糊查询账户信息（用于联想下拉控件）
     * @param keyword 关键字，匹配户名或公司账户
     * @return 账户信息列表
     */
    List<TbZjjgAcctInfo> suggestAcctInfo(String keyword);

    /**
     * 分页查询账户信息
     * @param queryVo 查询条件
     * @param pageNum 当前页
     * @param pageSize 每页数量
     * @return 分页账户信息列表
     */
    PageInfo<TbZjjgAcctInfo> queryAcctInfoPage(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize);

    /**
     * 新增账户信息
     * @param acctInfo 账户信息
     * @return 是否成功
     */
    boolean addAcctInfo(TbZjjgAcctInfo acctInfo);

    /**
     * 修改账户信息
     * @param acctInfo 账户信息
     * @return 是否成功
     */
    boolean updateAcctInfo(TbZjjgAcctInfo acctInfo);

    /**
     * 删除账户信息
     * @param cpabAccId 公司账户
     * @param acctNm 户名
     * @return 是否成功
     */
    boolean deleteAcctInfo(String cpabAccId, String acctNm);

    /**
     * 根据公司账户和户名查询账户信息
     * @param cpabAccId 公司账户
     * @param acctNm 户名
     * @return 账户信息
     */
    TbZjjgAcctInfo getAcctInfo(String cpabAccId, String acctNm);

    /**
     * 获取账户敏感信息（脱敏/不脱敏）
     * @param cpabAccId 公司账户
     * @param acctNm 户名
     * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
     * @return 账户信息
     */
    TbZjjgAcctInfo getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus);

    /**
     * 根据业务标识获取账户敏感信息（脱敏/不脱敏）- 使用oth_msg2_tx作为唯一标识
     * @param othMsg2Tx 唯一标识
     * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
     * @return 账户信息
     */
    TbZjjgAcctInfo getAcctSensitiveInfoByOthMsg2Tx(String othMsg2Tx, String sensitiveStatus);

    /**
     * 根据oth_msg2_tx获取账户详情
     * @param othMsg2Tx 唯一标识
     * @return 账户信息
     */
    TbZjjgAcctInfo getAcctInfoByOthMsg2Tx(String othMsg2Tx);

    /**
     * 根据oth_msg2_tx修改账户信息
     * @param acctInfo 账户信息（必须包含othMsg2Tx字段）
     * @return 是否成功
     */
    boolean updateAcctInfoByOthMsg2Tx(TbZjjgAcctInfo acctInfo);

    /**
     * 根据oth_msg2_tx删除账户信息
     * @param othMsg2Tx 唯一标识
     * @return 是否成功
     */
    boolean deleteAcctInfoByOthMsg2Tx(String othMsg2Tx);

    /**
     * 根据业务标识获取账户敏感信息（脱敏/不脱敏）- 兼容旧接口
     * @param tranTime 交易时间戳
     * @param opeCd 业务代码
     * @param merchId 委托单位代码
     * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
     * @return 账户信息
     */
    TbZjjgAcctInfo getAcctSensitiveInfoByBizKey(String tranTime, String opeCd, String merchId, String sensitiveStatus);
}