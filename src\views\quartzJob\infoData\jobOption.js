import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增定时任务',
    viewTitle: '查看定时任务',
    editTitle: '修改定时任务',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    // excelBtn:true,
    column: [
        {
            label: '任务类名',
            prop: 'jobClassName',
            search: true,
            width: 300,
            rules: [
                {
                    required: true,
                    message: '请输入任务类名',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 64,
                    message: '长度在 1 到 64 个字符',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: 'cron表达式',
            prop: 'cronExpression',
            slot: true,
            rules: [
                {
                    required: true,
                    message: '请输入cron表达式',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 64,
                    message: '长度在 1 到 64 个字符',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '参数',
            prop: 'parameter',
            type: 'textarea',
            dicData: [],
        },
        {
            label: '描述',
            prop: 'description',
            type: 'textarea',
            rules: [
                {
                    required: true,
                    message: '请输入该定时器描述',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 64,
                    message: '长度在 1 到 64 个字符',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '状态',
            prop: 'status',
            type: 'radio',
            search: true,
            slot: true,
            dicData: [],
            width: 80,
            rules: [
                {
                    required: true,
                    message: '请选择状态',
                    trigger: 'change',
                },
            ],
        },
    ],
};
