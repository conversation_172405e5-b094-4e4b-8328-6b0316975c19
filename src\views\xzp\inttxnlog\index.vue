<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" :rules="rules" ref="searchForm" class="search-form" label-width="20px" size="small">
        <el-row :gutter="24">
          
          <el-col :span="10">
            <el-form-item label="业务代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.opeCd"
                  :fetch-suggestions="queryOpeCd"
                  placeholder="请输入业务代码"
                  @select="handleOpeCdSelect"
                  @clear="handlerOpeCdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value +'-'+ item.item.opeNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.opeNm" class="tip-text">{{ search.opeNm }}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.merchId"
                  :fetch-suggestions="queryMerchId"
                  placeholder="请输入委托单位代码"
                  @select="handleMerchIdSelect"
                  @clear="handlerMerchIdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.prdtNm}}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.merchNm" class="tip-text">{{ search.merchNm }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item label="起始日期">
              <el-date-picker
                class="custom-input"
                v-model="search.startTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择起始日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="终止日期">
              <el-date-picker
                class="custom-input"
                v-model="search.endTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择终止日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item label="查询参数" class="form-item-with-tip">
              <el-select
                v-model="search.searchType"
                placeholder="请选择查询参数"
                clearable
                class="custom-input"
                @change="handleSearchTypeChange"
              >
                <el-option
                  v-for="dict in dict.type['xzp_txn_log_search_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10" v-show="search.searchType === '1'">
            <el-form-item label="卡号" prop="accCardId">
              <el-input v-model="search.accCardId" clearable placeholder="请输入卡号" class="custom-input" />
            </el-form-item>
          </el-col>

          <el-col :span="10" v-show="search.searchType === '2'">
            <el-form-item label="子业务代码" prop="subOpeCd" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.subOpeCd"
                  :fetch-suggestions="querySubOpeCd"
                  placeholder="请输入子业务代码"
                  @select="handleSubOpeCdSelect"
                  @clear="handlerSubOpeCdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.subOpeNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.subOpeNm" class="tip-text">{{ search.subOpeNm }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="10" v-show="search.searchType === '2'">
            <el-form-item label="用户号" prop="userCd">
              <el-input v-model="search.userCd" clearable placeholder="请输入用户号" class="custom-input" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据为空时的提示信息 -->
    <el-alert
      v-if="!data.length && !loading && !hasSearched"
      title="请设置查询条件后点击查询按钮获取数据"
      type="info"
      :closable="false"
      style="margin-bottom: 16px;"
      show-icon
    />

    <!-- 查询后无数据的提示 -->
    <el-alert
      v-if="!data.length && !loading && hasSearched"
      title="未查询到符合条件的数据"
      type="warning"
      :closable="false"
      style="margin-bottom: 16px;"
      show-icon
    />

    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <!-- 交易说明字段省略显示 -->
      <template slot="othMsg7Tx" slot-scope="scope">
        <el-tooltip
          :content="scope.row.othMsg7Tx"
          placement="top"
          :disabled="!shouldShowTooltip(scope.row.othMsg7Tx)"
        >
          <span class="text-ellipsis">
            {{scope.row.othMsg7Tx}}
          </span>
        </el-tooltip>
      </template>

      <template slot="payId" slot-scope="scope">
        <div>
          <span>{{scope.row.payId}}</span>
          <el-button
            v-if="scope.row.payId"
            type="text"
            icon="el-icon-view"
            @click="viewInfo(scope.row,'payId')"
          ></el-button>
        </div>
      </template>
      <template slot="accCardId" slot-scope="scope">
        <div>
          <span>{{scope.row.accCardId}}</span>
          <el-button
            v-if="scope.row.accCardId"
            type="text"
            icon="el-icon-view"
            @click="viewInfo(scope.row,'accCardId')"
          ></el-button>
        </div>
      </template>
      <template slot-scope="{row,size,type}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          @click="handleCommand(row)"
          icon="el-icon-info"
        >查看</el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { intTxnLogList } from '@/api/inttxnlog';
import intTxnLogOption from './infoData/intTxnLogOption.js';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import { listGroupBySubOpeId } from '@/api/subope';

export default {
  name: 'inttxnlog',
  dicts: ['xzp_txn_log_search_type'],
  data() {
    return {
      loading: false,
      formParent: {},
      search: {
        searchType: '1',
        merchId: '',
        merchNm: '',
        opeCd: '',
        opeNm: '',
        payId: '',
        accCardId: '',
        startTime: '',
        endTime: '',
        subOpeCd: '',
        subOpeNm: '',
        userCd: '',
      },
      rules: {
        accCardId: [
          { required: true, message: '请输入卡号', trigger: 'blur' }
        ],
        subOpeCd: [
          { required: true, message: '请输入子业务代码', trigger: 'blur' }
        ],
        userCd: [
          { required: true, message: '请输入用户号', trigger: 'blur' }
        ]
      },
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      data: [],
      option: intTxnLogOption,
      multiple: true,
      hasSearched: false, // 标记是否已经执行过查询
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false,
      subOpeCdLoading: false,

    };
  },
  created() {
    this.page.currentPage = 1;
    /** 添加字典项数据 */
    // this.updateDictData(
    //   this.option.column,
    //   'searchType',
    //   this.dict.type['xzp_txn_log_search_type']
    // );
    // this.getList();
  },
  methods: {
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    // 查找对象
    findObject(option, key) {
      return option.find(item => item.prop === key);
    },
    onLoad() {
      // 不自动触发查询，需要用户手动点击查询按钮
      // this.getList();
    },
    handleCommand(row) {
      this.$refs.crud.rowView(row);
    },
    /** 判断是否需要显示 tooltip */
    shouldShowTooltip(text) {
      if (!text) return false;
      // 如果文本长度超过15个字符，则显示tooltip
      return text.length > 10;
    },
    /** 清除特定字段的验证状态 */
    clearFieldValidation(fields) {
      if (this.$refs.searchForm) {
        fields.forEach(field => {
          this.$refs.searchForm.clearValidate(field);
        });
      }
    },
    /** 处理查询类型变化 */
    handleSearchTypeChange(value) {
      // 清除所有字段的验证状态
      this.clearFieldValidation(['accCardId', 'subOpeCd', 'userCd']);

      // 根据查询类型清空相关字段
      if (value === '1') {
        // 切换到卡号查询，清空子业务代码相关字段
        this.search.subOpeCd = '';
        this.search.subOpeNm = '';
        this.search.userCd = '';
      } else if (value === '2') {
        // 切换到子业务代码查询，清空卡号字段
        this.search.accCardId = '';
      }
    },
    /** 敏感信息显隐 */
    viewInfo(row, type) {
      const originalKey = `_original_${type}`;
      if (row[originalKey]) {
        row[type] = row[originalKey];
        delete row[originalKey];
      } else {
        const originalValue = row[type];
        if (!originalValue) return;
        row[originalKey] = originalValue;
        row[type] = originalValue.slice(0, 3) + '*'.repeat(Math.max(originalValue.length - 7, 0)) + originalValue.slice(-4);
      }
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    async getList() {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      this.hasSearched = true; // 标记已经执行过查询
      try {
        const { code, data } = await intTxnLogList(params);
        if (code === '0') {
          this.data = data.list || [];
          this.page.total = data.total;
          
          // 在数据加载后默认进行脱敏
          this.data.forEach(row => {
            ['payId', 'accCardId'].forEach(type => {
              const value = row[type];
              if (value) {
                row[`_original_${type}`] = value;
                row[type] = value.slice(0, 3) + '*'.repeat(Math.max(value.length - 7, 0)) + value.slice(-4);
              }
            });
          });
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      // 根据查询类型进行动态验证
      if (this.search.searchType === '1') {
        // 卡号查询：只验证卡号字段
        this.$refs.searchForm.validateField('accCardId', (errorMessage) => {
          if (!errorMessage) {
            this.page.currentPage = 1;
            this.getList();
          }
        });
      } else if (this.search.searchType === '2') {
        // 子业务代码查询：只验证子业务代码和用户号字段
        const fieldsToValidate = ['subOpeCd', 'userCd'];
        let validationErrors = [];
        let validatedCount = 0;

        fieldsToValidate.forEach(field => {
          this.$refs.searchForm.validateField(field, (errorMessage) => {
            validatedCount++;
            if (errorMessage) {
              validationErrors.push(errorMessage);
            }

            // 所有字段验证完成后执行查询
            if (validatedCount === fieldsToValidate.length) {
              if (validationErrors.length === 0) {
                this.page.currentPage = 1;
                this.getList();
              }
            }
          });
        });
      } else {
        // 其他查询类型直接查询
        this.page.currentPage = 1;
        this.getList();
      }
    },
    resetQuery() {
      // 重置所有查询条件
      this.search.searchType = '1';
      this.search.merchId = '';
      this.search.merchNm = '';
      this.search.opeCd = '';
      this.search.opeNm = '';
      this.search.payId = '';
      this.search.accCardId = '';
      this.search.subOpeCd = '';
      this.search.subOpeNm = '';
      this.search.userCd = '';

      // 明确将时间字段设置为空，不设置任何默认值
      this.search.startTime = '';
      this.search.endTime = '';

      // 重置查询状态和数据
      this.hasSearched = false;
      this.data = [];

      // 清除所有字段的验证状态
      this.clearFieldValidation(['accCardId', 'subOpeCd', 'userCd']);
    },
    handleSelectionChange(selection) {
      this.multiple = !(selection.length > 0);
    },
    validateDateRange() {
      if (this.search.startTime && this.search.endTime) {
        const startDate = new Date(this.search.startTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
        const endDate = new Date(this.search.endTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));

        // 检查终止日期是否小于起始日期
        if (endDate < startDate) {
          this.$message.warning('终止日期不能小于起始日期');
          this.search.endTime = '';
          return;
        }
      }
    },
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opeCd
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 查询子业务代码
    async querySubOpeCd(queryString, cb) {
      this.subOpeCdLoading = true;
      try {
        const params = {
          subOpeId: queryString,
          opeCd: this.search.opeCd,
          merchId: this.search.merchId
        };
        const { code, data } = await listGroupBySubOpeId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.subOpeId,
            label: item.subOpeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching subOpeCd:', error);
        cb([]);
      } finally {
        this.subOpeCdLoading = false;
      }
    },

    // 清除方法
    handlerOpeCdClear(){
      this.search.opeCd = ''
      this.search.opeNm = ''
    },
    handlerMerchIdClear(){
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    handlerSubOpeCdClear() {
      this.search.subOpeCd = '';
      this.search.subOpeNm = '';
    },

    // 选择业务代码
    handleOpeCdSelect(item) {
      this.search.opeCd = item.value;
      this.search.opeNm = item.item.opeNm;
      // 清空委托单位代码，触发重新查询
      if (this.search.merchId) {
        this.search.merchId = '';
        this.search.merchNm = '';
      }
    },

    // 选择委托单位代码
    handleMerchIdSelect(item) {
      this.search.merchId = item.value;
      this.search.merchNm = item.item.prdtNm;
    },

    // 选择子业务代码
    handleSubOpeCdSelect(item) {
      this.search.subOpeCd = item.value;
      this.search.subOpeNm = item.item.subOpeNm;
    },
  },
};
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}

.form-item-with-tip {
  margin-bottom: 18px;
}

.input-with-tip {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-input {
  width: 240px !important;
}

.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
  padding-right: 50px;
}

.search-btns .el-button + .el-button {
  margin-left: 8px;
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-form-item__label) {
  width: 120px !important;
  padding-right: 0px !important;
}

:deep(.el-form-item__content) {
  margin-left: 10px !important;
}

/* 表格单元格省略显示样式 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  max-width: 130px; /* 根据列宽调整 */
  display: inline-block;
}

/* 自定义 tooltip 样式 */
:deep(.el-tooltip__popper) {
  max-width: 400px;
  word-wrap: break-word;
  word-break: break-all;
}
</style>