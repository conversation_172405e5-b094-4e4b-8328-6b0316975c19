# 脱敏查询优化说明

## 问题描述

在实现后端脱敏功能后，出现了一个新问题：
- 列表查询返回的数据中，`cpabAccId`和`acctNm`字段已经被脱敏
- 当用户点击显隐按钮切换脱敏状态时，使用脱敏后的值无法查询到原始记录
- 导致敏感信息切换功能失效

## 解决方案

使用业务标识字段（`tranTime`、`opeCd`、`merchId`）作为唯一标识来查询详情，替代脱敏后的字段。

## 修改内容

### 1. Service接口扩展

**文件**: `ITbZjjgAcctInfoService.java`

添加基于业务标识的查询方法：
```java
/**
 * 根据业务标识获取账户敏感信息（脱敏/不脱敏）
 * @param tranTime 交易时间戳
 * @param opeCd 业务代码
 * @param merchId 委托单位代码
 * @param sensitiveStatus 敏感状态：1-脱敏，2-不脱敏
 * @return 账户信息
 */
TbZjjgAcctInfo getAcctSensitiveInfoByBizKey(String tranTime, String opeCd, String merchId, String sensitiveStatus);
```

### 2. Service实现

**文件**: `TbZjjgAcctInfoServiceImpl.java`

实现基于业务标识的查询逻辑：
```java
@Override
public TbZjjgAcctInfo getAcctSensitiveInfoByBizKey(String tranTime, String opeCd, String merchId, String sensitiveStatus) {
    // 查询原始数据
    TbZjjgAcctInfo originalInfo = this.lambdaQuery()
            .eq(TbZjjgAcctInfo::getTranTime, tranTime)
            .eq(TbZjjgAcctInfo::getOpeCd, opeCd)
            .eq(TbZjjgAcctInfo::getMerchId, merchId)
            .one();

    // 根据敏感状态进行脱敏处理
    // ... 脱敏逻辑
}
```

### 3. API接口扩展

**文件**: `AcctInfoApi.java`

添加新的API端点：
```java
@ApiOperation(value = "根据业务标识获取账户敏感信息", notes = "getAcctSensitiveInfoByBizKey")
@GetMapping("/acctSensitiveByBizKey/{tranTime}/{opeCd}/{merchId}/{sensitiveStatus}")
RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(
        @PathVariable("tranTime") String tranTime,
        @PathVariable("opeCd") String opeCd,
        @PathVariable("merchId") String merchId,
        @PathVariable("sensitiveStatus") String sensitiveStatus
);
```

### 4. Controller实现

**文件**: `AcctInfoController.java`

实现新的API端点：
```java
@Override
public RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(String tranTime, String opeCd, String merchId, String sensitiveStatus) {
    TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctSensitiveInfoByBizKey(tranTime, opeCd, merchId, sensitiveStatus);
    if (result != null) {
        return RestResponse.success(result);
    } else {
        return RestResponse.fail("获取账户敏感信息失败，可能是记录不存在");
    }
}
```

### 5. 前端API调用

**文件**: `src/api/acctmgmt.js`

添加新的API调用方法：
```javascript
// 根据业务标识获取账户敏感信息（脱敏/不脱敏）
export function getAcctSensitiveInfoByBizKey(tranTime, opeCd, merchId, sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/acctSensitiveByBizKey/${encodeURIComponent(tranTime)}/${encodeURIComponent(opeCd)}/${encodeURIComponent(merchId)}/${sensitiveStatus}`,
    method: 'get'
  });
}
```

### 6. 前端页面优化

**文件**: `src/views/xzp/acctmgmt/index.vue`

修改敏感信息切换方法：
```javascript
// 敏感信息显隐切换
async viewSensitiveInfo(row, type) {
  try {
    // 检查必要的业务标识字段
    if (!row.tranTime || !row.opeCd || !row.merchId) {
      this.$message.error('缺少必要的业务标识信息，无法切换显示状态');
      return;
    }

    const currentStatus = row.sensitiveStatus || '1';
    const targetStatus = currentStatus === '1' ? '2' : '1';
    
    // 使用业务标识调用后端接口
    const response = await getAcctSensitiveInfoByBizKey(row.tranTime, row.opeCd, row.merchId, targetStatus);
    
    if (response && response.data) {
      const sensitiveData = response.data;
      // 更新当前行的敏感字段
      this.$set(row, 'cpabAccId', sensitiveData.cpabAccId);
      this.$set(row, 'acctNm', sensitiveData.acctNm);
      this.$set(row, 'sensitiveStatus', sensitiveData.sensitiveStatus);
      
      this.$message.success(targetStatus === '1' ? '已脱敏' : '已显示明文');
    }
  } catch (error) {
    this.$message.error('操作失败，请重试');
  }
}
```

## 业务标识字段说明

### 选择原因
- **tranTime（交易时间戳）**: 精确到秒的时间戳，具有很强的唯一性
- **opeCd（业务代码）**: 业务类型标识
- **merchId（委托单位代码）**: 委托方标识

### 唯一性保证
这三个字段的组合在业务上具有唯一性，能够准确定位到具体的账户记录。

## 优势

1. **解决脱敏查询问题**: 使用未脱敏的业务标识字段进行查询
2. **保持数据安全**: 敏感字段（账户号、账户名）仍然进行脱敏处理
3. **业务逻辑清晰**: 基于业务标识的查询更符合业务逻辑
4. **向后兼容**: 保留原有的查询方法，不影响其他功能

## 测试要点

1. **脱敏状态切换**: 确认点击显隐按钮能正确切换脱敏/明文状态
2. **业务标识验证**: 确认必要的业务标识字段存在时才能切换
3. **错误处理**: 缺少业务标识字段时有适当的错误提示
4. **数据一致性**: 切换后的数据与原始记录一致

## 注意事项

1. **字段完整性**: 确保列表查询返回的数据包含完整的业务标识字段
2. **URL编码**: API调用时对参数进行URL编码，避免特殊字符问题
3. **错误处理**: 完善的错误处理和用户提示
4. **性能考虑**: 基于索引字段的查询，确保查询性能
