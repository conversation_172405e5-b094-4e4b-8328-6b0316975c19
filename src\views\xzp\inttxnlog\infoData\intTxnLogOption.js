export default {
    index: true,
    indexLabel: '序号',
    addBtn: false,
    reserveSelection: true,
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    menu: true,
    editBtn: false,
    delBtn: false,
    menuWidth: 80,
    tip: false,
    columnBtn: false,
    labelWidth: 120,
    labelPosition: 'right',
    viewTitle: '内部交易日志查询',
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    column: [
      { label: '交易日期', prop: 'tranDt' },
      { label: '交易时间', prop: 'localTm' },
      { label: '交易机构', prop: 'tranInstId' },
      { label: '交易柜员', prop: 'tlrId' },
      { label: '交易代码', prop: 'tranCd' },
      { label: '交易说明', prop: 'othMsg7Tx', slot: true, width: 150 },
      { label: '业务代码', prop: 'opeCd' },
      { label: '业务代码名称', prop: 'opeNm',width: 140 },
      { label: '委托单位', prop: 'merchId' },
      { label: '委托单位名称', prop: 'merchNm', width: 120 },
      { label: '子业务代码', prop: 'subOpeCd', width: 120 },
      { label: '子业务代码名称', prop: 'subOpeNm',width: 140 },
      { label: '组合用户号', prop: 'payId',width: 120 },
      { label: '缴费卡号', prop: 'accCardId' },
      { label: '金额', prop: 'tranAt' },
      { label: '发票号', prop: 'invoiceNum'},
      { label: '缴费校验码', prop: 'payCd',width: 140 },
      { label: '交易状态', prop: 'tranStatCd' },
      { label: '交易应答码', prop: 'rspCd',width: 140 },
      { label: '应答码说明',prop: 'othPrMsg3',width: 140},
      { label: '消息id',prop: 'logId'}
    ]
  };