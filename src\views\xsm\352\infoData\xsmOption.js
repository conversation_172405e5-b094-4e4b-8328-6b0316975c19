import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'id',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '',
    addTitle: '添加白鹭分查询',
    addBtn: true,
    addBtnText: '添加白鹭分查询',
    addBtnIcon: 'el-icon-search',
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchShowBtn: false,
    searchRow: false,
    labelWidth: 120,
    dialogWidth: 800,
    columnBtn: false,
    excelBtn: false,
    menuWidth: 100,
    column: [
        {
            label: '查询批次号',
            prop: 'queryNo',
            search: true,
            editDisabled: false,
            // searchSpan: 8,
            addDisplay: false,
            searchLabelWidth: 90,
            width: 160,
        },
        {
            label: '姓名',
            prop: 'name',
            search: true,
            editDisabled: false,
            searchSpan: 5,
            searchLabelWidth: 50,
            rules: [
                {
                    required: true,
                    message: '请输入用户名',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '身份证号码',
            prop: 'idCard',
            // searchSpan: 8,
            search: true,
            searchLabelWidth: 90,
            slot: true,
            width: 170,
            rules: [
                {
                    required: true,
                    message: '请输入身份证号',
                    trigger: 'blur',
                },
                {
                    validator: validate.checkIdentity,
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '申请时间',
            prop: 'createTime',
            showColumn: false,
            addDisplay: false,
            searchslot: true,
            search: true,
            dicData: [],
            width: 160,
        },
        {
            label: '状态',
            prop: 'status',
            searchLabelWidth: 50,
            searchSpan: 5,
            search: true,
            type: 'select',
            slot: true,
            formslot: true,
            dicData: [],
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '白鹭分',
            prop: 'score',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '新市民类别',
            prop: 'citizentype',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
    ],
};
