import validate from "@/utils/validate"

export default {
  index:true,
  indexLabel:'序号',
  selection: true,
  menuWidth:100,
  align: 'center',
  card: true,
  menuAlign: 'center',
  addTitle: '新增',
  editTitle: '修改',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  searchMenuSpan:6,
  // searchMenuPosition:'left',
  searchLabelWidth:100,
  labelPosition: 'right',
  labelWidth: 150,
  tip:false,
  columnBtn:false,
  column: [
    {
      label: '系统编号',
      prop: 'systemCode',
      search: true,
      type: "select",
      dicData: [],
    },
    {
      label: '操作人',
      prop: 'createUser',
      search:true,
    },
    {
      label: '操作人姓名',
      prop: 'createNickname',
      search: true,
    },
    {
      label: '操作类型',
      prop: 'action',
      type: "select",
      search: true,
      dicData: [],
      hide: true,
    },
    {
      label: '交易名称',
      prop: 'message',
      search: true,
    },
    {
      label: '执行方法',
      prop: 'executeMethod',
      hide: true,
      showColumn: false,
      formslot:true,
    },
    {
      label: '上下文数据',
      prop: 'content',
      hide: true,
      showColumn: false,
      formslot:true,
    },
    {
      label: '请求地址',
      prop: 'requestURI',
      hide: true,
      showColumn: false,
    },
    {
      label: '请求方式',
      prop: 'requestType',
      hide: true,
    },
    {
      label: '操作IP',
      prop: 'operateIP',
    },
    {
      label: '操作状态',
      prop: 'status',
      search: true,
      slot: true,
      viewDisplay:false,
      type: "select",
      dicData: [],
    },
    {
      width: 150,
      label: '操作时间',
      prop: 'operateTime',
      searchslot:true,
      search: true,
      viewDisplay:false,
    },
    {
      label: '请求耗时',
      prop: 'totalTime',
    },
    {
      label: '记录时间',
      prop: 'recordTime',
      hide: true,
      showColumn: false,
    },
  ]
}
