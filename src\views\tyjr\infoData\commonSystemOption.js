import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增系统',
    viewTitle: '查看详情',
    editTitle: '修改系统',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    // excelBtn:true,
    column: [
        {
            label: '系统编号',
            width: 120,
            prop: 'sysNo',
            addDisabled: true,
            editDisabled: true,
            formslot: true,
        },
        {
            label: '系统名称',
            width: 120,
            prop: 'sysName',
            search: true,
            rules: [
                {
                    required: true,
                    message: '请输入系统名称',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 64,
                    message: '长度在 1 到 64 个字符',
                    trigger: 'blur',
                },
                {
                    validator: validate.userIdValidate,
                    trigger: 'blur',
                },
            ],
        },

        {
          label: '访问网络类型',
          prop: 'accessNetworkType',
          type: 'select',
          slot:true,
          dicData: [],
          rules: [{
            required: true,
            message: "请选择访问网络类型",
            trigger: "change"
          }]
        },

        {
            label: '登录地址',
            prop: 'loginAddr',
            rules: [
                {
                    required: true,
                    message: '请输入登录地址',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 64,
                    message: '长度在 1 到 64 个字符',
                    trigger: 'blur',
                },
                {
                    validator: validate.blankSpace,
                    trigger: 'blur',
                },
            ],
        },

        {
            label: '排序',
            prop: 'sort',
            width: 80,
            rules: [
                {
                    required: true,
                    message: '请输入排序',
                    trigger: 'blur',
                },
            ],
        },
    ],
};
