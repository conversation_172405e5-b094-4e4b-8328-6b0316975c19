# 监管账户管理页面修改总结

## 修改目标
将原本使用 `tranTime`、`opeCd`、`merchId` 作为唯一值查询详情记录的逻辑，改为使用 `oth_msg2_tx` 作为唯一值来查询详情修改记录，并在新增时为该值添加 UUID。

## 修改文件列表

### 1. src/api/acctmgmt.js
**修改内容：**
- 修改 `getAcctSensitiveInfoByBizKey` 函数，将参数从 `(tranTime, opeCd, merchId, sensitiveStatus)` 改为 `(othMsg2Tx, sensitiveStatus)`
- 新增 `getAcctInfoByOthMsg2Tx` 函数，根据 `oth_msg2_tx` 获取账户详情
- 新增 `deleteAcctInfoByOthMsg2Tx` 函数，根据 `oth_msg2_tx` 删除账户信息

**新增API接口：**
```javascript
// 根据oth_msg2_tx获取账户详情
export function getAcctInfoByOthMsg2Tx(othMsg2Tx)

// 根据oth_msg2_tx删除账户信息
export function deleteAcctInfoByOthMsg2Tx(othMsg2Tx)

// 根据oth_msg2_tx修改账户信息
export function updateAcctInfoByOthMsg2Tx(data)
```

### 2. src/views/xzp/acctmgmt/infoData/acctMgmtOption.js
**修改内容：**
- 在表格配置中新增 `othMsg2Tx` 字段配置
- 该字段设置为隐藏，仅在查看详情时显示
- 设置为只读，系统自动生成

**新增字段配置：**
```javascript
{
  label: '唯一标识',
  prop: 'othMsg2Tx',
  hide: true,
  showColumn: false,
  addDisplay: false,
  editDisplay: false,
  viewDisplay: true,
  span: 12,
  readonly: true,
  placeholder: '系统自动生成'
}
```

### 3. src/views/xzp/acctmgmt/index.vue
**主要修改：**

#### 3.1 新增UUID生成函数
```javascript
generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
```

#### 3.2 修改新增方法 `rowSave`
- 在新增记录时自动为 `othMsg2Tx` 字段生成UUID
- 确保每条新记录都有唯一标识

#### 3.3 修改更新方法 `rowUpdate`
- 优先使用 `updateAcctInfoByOthMsg2Tx` 进行更新
- 兼容旧数据，如果没有 `oth_msg2_tx` 则使用原来的 `updateAcctInfo`

#### 3.4 修改删除相关方法
- `rowDel`: 优先使用 `oth_msg2_tx` 进行删除，兼容旧数据
- `handleSingleDelete`: 使用 `oth_msg2_tx` 作为删除标识
- `handleDelete`: 批量删除时使用 `oth_msg2_tx` 作为删除标识

#### 3.5 修改查看详情方法 `handleView`
- 优先使用 `getAcctInfoByOthMsg2Tx` 获取最新数据
- 如果获取失败则使用当前行数据作为兜底

#### 3.6 修改敏感信息显隐方法 `viewSensitiveInfo`
- 将检查条件从 `tranTime`、`opeCd`、`merchId` 改为检查 `othMsg2Tx`
- 使用 `oth_msg2_tx` 调用敏感信息接口

## 兼容性处理
为了确保系统的稳定性，所有修改都包含了兼容性处理：

1. **删除操作兼容性**：如果记录没有 `oth_msg2_tx`，则使用原来的 `cpabAccId` 和 `acctNm` 进行删除
2. **查看详情兼容性**：如果通过 `oth_msg2_tx` 获取数据失败，则直接使用当前行数据
3. **敏感信息兼容性**：只有存在 `oth_msg2_tx` 的记录才能进行敏感信息切换

## 数据库字段要求
后端需要确保：
1. 数据库表中存在 `oth_msg2_tx` 字段
2. 该字段应设置为唯一索引
3. 新增记录时该字段不能为空
4. 相关API接口支持基于 `oth_msg2_tx` 的CRUD操作

## 测试建议
1. 测试新增记录时 `oth_msg2_tx` 是否正确生成UUID
2. 测试基于 `oth_msg2_tx` 的查看、修改、删除功能
3. 测试敏感信息显隐功能是否正常
4. 测试兼容性：对于没有 `oth_msg2_tx` 的旧数据是否能正常操作
5. 测试批量删除功能

## 注意事项
1. 确保后端API接口已经实现并测试通过
2. 数据库迁移脚本需要为现有数据生成 `oth_msg2_tx` 值
3. 前端修改完成后需要进行充分的功能测试
4. 建议在测试环境充分验证后再部署到生产环境
