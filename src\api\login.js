import request from '@/utils/request'

// 获取认证方式
export function loginType (data) {
  return request({
    url: '/api/auth/center/auth/mode',
    headers: {
      isToken: false
    },
    method: 'get',
    data: data
  })
}
// 获取跳转链接
export function uaasPortalurl (data) {
  return request({
    url: '/api/auth/center/uaas/portal-url',
    headers: {
      isToken: false
    },
    method: 'get',
    data: data
  })
}
// 6.84.动态令牌登录接口（统一认证登录时使用）
export function uaasTokenLogin (data) {
  return request({
    //url: '/api/auth/center/uaas/token/actions/login',  //原总行使用的统一认证登录
    url: `/api/admin/tyjr/uaas/login`, //202404改造：分行使用统一前置进行统一认证登录
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 20240426改造，新增接口：动态令牌刷新接口（统一认证登录时使用）
export function uaasTokenRefresh (data) {
  return request({
    url: `/api/admin/tyjr/uaas/refresh`,
    method: 'post',
    data: data
  })
}

// 获取验证码接口（统一认证登录时使用）
export function getuaasLogin (data) {
  return request({
    url: '/api/auth/center/uaas/captcha',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 登录方法
export function login (data) {
  return request({
    //url: '/login',
    url: '/api/auth/center/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 注册方法
export function register (data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

//  获取用户信息 包括基本信息，角色和权限按钮信息
export function getInfo (data) {
  const {systemCode = '', currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/sys/user/actions/get?systemCode=${systemCode}`,
    method: 'get'
  })
}

// 退出方法
export function logout () {
  return request({
    //url: '/logout',
    url: '/api/auth/center/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg () {
  return request({
    url: '/api/auth/center/captcha/image',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
// 获取短信验证码
export function getCodeSms (params) {
  return request({
    url: '/api/auth/center/captcha/sms',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000,
    data:params
  })
}
// 获取短信验证码后查询验证码
export function getCodePhoneSms (params) {
  return request({
    url: `/api/auth/center/captcha/sms/${params}/get`,
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
// 令牌刷新接口
export function getTokenRefresh (data) {
  return request({
    url: '/api/auth/center/actions/refresh',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
// 修改密码
export function alterPassword (data) {
  return request({
    url: '/api/auth/center/passwd/actions/change',
    method: 'post',
    data: data
  })
}


