import { login, uaasTokenLogin, logout, getInfo, getTokenRefresh } from '@/api/login'
import { setLogonCredentials, removeLogonCredentials, getAccessToken, getRefreshToken, setAuthMode, removeAuthMode, setUAASToken, removeUAASToken } from '@/utils/auth'
import {systemType} from '@/utils/constant'
import { formatDate } from "@/utils/index"
import { syncYoafToken } from "@/api/tyjr"
import {uaasTokenRefresh} from "../../api/login";

const user = {
  state: {
    // 访问令牌
    accessToken: getAccessToken(),
    // 刷新令牌
    refreshToken: getRefreshToken(),
    // 登录名
    name: '',
    // 姓名
    userNickname: '',
    // 头像
    avatar: '',
    // 角色集合
    roles: [],
    // 权限按钮集合
    permissions: [],
    // 修改密码标识 0-未修改 1-已修改
    pwdChgFlag:'',
    // 密码过期标识 0-未过期 1-已过期
    pwdExpiredFlag:'',
    // 用户ID
    userId: '',
    // 机构ID
    instId: '',
    // 机构级别
    instLvl: '',
    // 员工代码
    empCode: '',
    // 机构代码
    orgCode: '',
    // 机构等级
    orgDegree: ''
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.accessToken = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NICK_NAME: (state, userNickname) => {
      state.userNickname = userNickname
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_PWDCHGFLAG: (state, pwdChgFlag) => {
      state.pwdChgFlag = pwdChgFlag
    },
    SET_PWDEXPIREDFLAG: (state, pwdExpiredFlag) => {
      state.pwdExpiredFlag = pwdExpiredFlag
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_REFRESHTOKEN: (state, refreshToken) => {
      state.refreshToken = refreshToken
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
    },
    SET_INSTID: (state, instId) => {
      state.instId = instId
    },
    SET_INSTLVL: (state, instLvl) => {
      state.instLvl = instLvl
    },
    SET_EMPCODE: (state, empCode) => {
      state.empCode = empCode
    },
    SET_ORGCODE: (state, orgCode) => {
      state.orgCode = orgCode
    },
    SET_ORGDEGREE: (state, orgDegree) => {
      state.orgDegree = orgDegree
    },
  },

  actions: {
    // 登录
    Login ({ commit, state }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(response => {
          if (response?.code === '0') {
            const res = response.data
            // 客户端缓存登录凭证
            setLogonCredentials(res)
            // 客户端缓存统一认证令牌
            const uaasToken = res?.additionalParameters?.['UAAS-Token']
            if (uaasToken) {
              setUAASToken(uaasToken)
            }
            commit('SET_TOKEN', res?.accessToken?.tokenValue)
            commit('SET_REFRESHTOKEN', res?.refreshToken?.tokenValue)
            resolve()

            //登录成功，则设置相关字段到前端localStorage里
            localStorage.setItem('userToken', res.accessToken.tokenValue);
            localStorage.setItem('channel', "xmueps");

            //将yoaf框架生成的redis的用户信息存到另一个onlineUser的key里，使得二者信息保持同步
            let data = {userToken: localStorage.getItem('userToken'), channel:localStorage.getItem('channel')}
            syncYoafToken(data).then(response => {})
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 动态令牌登录
    UaasTokenLogin ({ commit, state }, uaasToken) {

      console.log("邮连跳转超链接如下：");
      console.log(window.location.href);
      const params = new URLSearchParams(window.location.search);
      const userToken = params.get("userToken");
      const uepsArea = params.get("uepsArea");

      return new Promise((resolve, reject) => {
        uaasTokenLogin(uaasToken).then(response => {
          if (response?.code === '0') {
            //const res = response.data
            const res = response.data.tokenRes
            // 客户端缓存登录凭证
            setLogonCredentials(res)
            // 客户端缓存统一认证令牌
            const uaasToken = res?.additionalParameters?.['UAAS-Token']
            if (uaasToken) {
              setUAASToken(uaasToken)
            }
            setAuthMode('UAAS')
            commit('SET_TOKEN', res?.accessToken?.tokenValue)
            commit('SET_REFRESHTOKEN', res?.refreshToken?.tokenValue)
            resolve()

            //统一认证登录成功，则设置相关字段到前端localStorage里
            localStorage.setItem('channel', 'uaas');  //说明是邮连登录，记录标志到浏览器本地缓存里
            localStorage.setItem('userToken', userToken); //将邮连的userToken存到本地缓存里
            localStorage.setItem('uepsArea', uepsArea); //将邮连的uepsArea存到本地缓存里

            localStorage.setItem('uaas_token_expireSeconds', response.data.uaas_token_expireSeconds);
            localStorage.setItem('uaas_token_startTime', formatDate(response.data.uaas_token_startTimestamp));
            localStorage.setItem('uaas_token_invalidTime', formatDate(response.data.uaas_token_invalidTimestamp));
            /*localStorage.setItem('uaas_token_refreshTime', formatDate(response.data.uaas_token_refreshTimestamp));
            localStorage.setItem('uaas_token_refreshTimestamp', response.data.uaas_token_refreshTimestamp);*/
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息
    GetInfo ({ commit, state }) {
      return new Promise((resolve, reject) => {
        let params = systemType
        getInfo(params).then(response => {
          const res = response.data
          // 登录名
          commit('SET_NAME', res?.userName || '')
          // 姓名
          commit('SET_NICK_NAME', res?.userNickname || '未知姓名')
          // 修改密码标识 0-未修改 1-已修改
          commit('SET_PWDCHGFLAG', res?.pwdChgFlag || 0)
          // 密码过期标识 0-未过期 1-已过期
          commit('SET_PWDEXPIREDFLAG', res?.pwdExpiredFlag || 1)
          // 用户ID
          commit('SET_USERID', res?.userId || res?.userName || '')
          // 机构ID
          commit('SET_INSTID', res?.instId || '')
          // 机构级别
          commit('SET_INSTLVL', res?.instLvl || res?.brhLvl || '')
          // 员工代码 (通常与用户名相同)
          commit('SET_EMPCODE', res?.empCode || res?.userId || '')
          // 机构代码 (通常与机构ID相同)
          commit('SET_ORGCODE', res?.orgCode || res?.instId || '')
          // 机构等级 (与机构级别相同)
          commit('SET_ORGDEGREE', res?.orgDegree || res?.instLvl || res?.brhLvl || '')
          // const avatar = user.avatar == "" ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
          // 默认头像
          const avatar = require("@/assets/images/profile.jpg")
          // 头像
          commit('SET_AVATAR', avatar)
          // 角色集合
          commit('SET_ROLES', res?.roleIds || [])
          // 权限按钮返回数据
          const permissions = res?.funcBtns || []
          let funcBtnsArr = []
          // 权限按钮数据处理：[{'yoaf': [T]}, {'XXX', [T]}] => [T]
          permissions.forEach((mapData, index) => {
            Object.values(mapData).forEach((item, indexItem) => {
              funcBtnsArr = funcBtnsArr.concat(item)
            })
          })
          // 权限按钮集合
          commit('SET_PERMISSIONS', funcBtnsArr)
          // 登录模式
          setAuthMode(res?.loginType || 'auth')
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 刷新token
    tokenRefresh ({ commit, state }) {
      return new Promise((resolve, reject) => {
        getTokenRefresh({ refreshToken: getRefreshToken() }).then((response) => {
          if (response?.code === '0') {
            const res = response.data
            // 客户端缓存登录凭证
            setLogonCredentials(res)
            // 客户端缓存统一认证令牌
            const uaasToken = res?.additionalParameters?.['UAAS-Token']
            if (uaasToken) {
              setUAASToken(uaasToken)
            }
            commit('SET_TOKEN', res?.accessToken?.tokenValue)
            commit('SET_REFRESHTOKEN', res?.refreshToken?.tokenValue)
            resolve(res?.accessToken?.tokenType?.value + ' ' + res?.accessToken?.tokenValue)


            //--------统一接入改造，新增代码 开始---------------------------------------------------
            //若当前是邮连跳转过来的，则需要调统一前置刷新接口，保持token不掉线
            const userToken = localStorage.getItem('userToken')
            const channel = localStorage.getItem('channel')
            if(channel=='uaas'){ //说明是邮连跳转登录的
              uaasTokenRefresh({userToken:userToken}).then(res => {
                localStorage.setItem('psbc-uaas-token', res.data.uaas_newToken); //更新邮连域下的token
                localStorage.setItem('userToken', res.data.uaas_newToken); //将邮连的userToken存到本地缓存里
                localStorage.setItem('uaas_token_expireSeconds', res.data.uaas_token_expireSeconds);
                localStorage.setItem('uaas_token_startTime', formatDate(res.data.uaas_token_startTimestamp));
                localStorage.setItem('uaas_token_invalidTime', formatDate(res.data.uaas_token_invalidTimestamp));
              });
            }else{ //说明本地登录的
              localStorage.setItem('userToken', res.accessToken.tokenValue); //将yoaf框架的新token更新到localStorage
            }

            //将yoaf框架生成的redis的用户信息存到另一个onlineUser的key里，使得二者信息保持同步
            let data = {userToken: localStorage.getItem('userToken'), channel:channel}
            syncYoafToken(data).then(response => {})
            //--------统一接入改造，新增代码 结束-------------
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })

        /*//202404改造：刷新token后台需判断是否调邮连统一前置接口刷新token
        let from = localStorage.getItem('channel');
        let token = localStorage.getItem('token');
        getTokenRefresh({ refreshToken: getRefreshToken(), from: from, token: token,  }).then((response) => {
          if (response?.code === '0') {
            //const res = response.data
            const res = response.data.res
            // 客户端缓存登录凭证
            setLogonCredentials(res)
            // 客户端缓存统一认证令牌
            const uaasToken = res?.additionalParameters?.['UAAS-Token']
            if (uaasToken) {
              setUAASToken(uaasToken)
            }
            commit('SET_TOKEN', res?.accessToken?.tokenValue)
            commit('SET_REFRESHTOKEN', res?.refreshToken?.tokenValue)
            resolve(res?.accessToken?.tokenType?.value + ' ' + res?.accessToken?.tokenValue)

            localStorage.setItem('token', response.data.token); //更新本地缓存里的token（若from是uaas则存的是统一前置的token,否则是本系统的）
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })*/
      })
    },
    // 退出系统
    LogOut ({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_REFRESHTOKEN', '')
          removeLogonCredentials()
          removeAuthMode()
          removeUAASToken()
          resolve()

          localStorage.removeItem('channel')
          localStorage.removeItem('userToken')
          localStorage.removeItem('uepsArea')
          localStorage.removeItem('uaas_token_expireSeconds')
          localStorage.removeItem('uaas_token_startTime')
          localStorage.removeItem('uaas_token_invalidTime')
          /*localStorage.removeItem('uaas_token_refreshTime')
          localStorage.removeItem('uaas_token_refreshTimestamp')*/
          localStorage.removeItem('psbc-uaas-token')
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 前端 登出
    FedLogOut ({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        commit('SET_REFRESHTOKEN', '')
        removeLogonCredentials()
        removeAuthMode()
        removeUAASToken()
        resolve()

        localStorage.removeItem('channel')
        localStorage.removeItem('userToken')
        localStorage.removeItem('uepsArea')
        localStorage.removeItem('uaas_token_expireSeconds')
        localStorage.removeItem('uaas_token_startTime')
        localStorage.removeItem('uaas_token_invalidTime')
        /*localStorage.removeItem('uaas_token_refreshTime')
        localStorage.removeItem('uaas_token_refreshTimestamp')*/
        localStorage.removeItem('psbc-uaas-token')
      })
    }
  }
}

export default user
