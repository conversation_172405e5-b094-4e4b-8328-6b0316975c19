<template>
  <div class="app-container">
    <yo-table
      v-loading="loading"
      :data="data"
      :option="option"
      ref="crud"
      :page.sync="page"
      @on-load="onLoad"
      :search.sync="search"
      :before-open="beforeOpen"
      @search-change="searchChange"
      @row-save="rowSave"
      @selection-change="handleSelectionChange"
      v-model="formParent"
    >
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:local:auths:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
      </template>

      <!-- 自定义弹窗表单 -->
      <template slot-scope="scope" slot="operRoleIdForm">
        <el-select v-model="formParent.operRoleId" clearable placeholder="请选择操作角色编号">
          <el-option
            v-for="item in rolesList"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
          ></el-option>
        </el-select>
      </template>
      <template slot-scope="scope" slot="authRoleIdForm">
        <el-select v-model="formParent.authRoleId" clearable placeholder="请选择授权角色编号">
          <el-option
            v-for="item in rolesList"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
          ></el-option>
        </el-select>
      </template>
      <template slot-scope="scope" slot="funcIdForm">
        <el-select v-model="formParent.funcId" filterable clearable placeholder="请选择功能名称">
          <el-option-group v-for="group in funcIdList" :key="group.menuName" :label="group.menuName">
            <el-option
              v-for="item in group.functionVOS"
              :key="item.id"
              :label="item.funcName"
              :value="item.id"
            ></el-option>
          </el-option-group>
        </el-select>
      </template>

      <!-- 自定义左侧操作栏 -->
      <template slot-scope="{size}" slot="menuLeft">
        <el-button
          type="primary"
          icon="el-icon-plus"
          :size="size"
          plain
          v-hasPermi="['admin:local:auths:add']"
          @click.stop="handleAdd"
        >新增</el-button>
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :size="size"
          :disabled="multiple"
          @click="handleDeletes"
          v-hasPermi="['admin:local:auths:remove:batch']"
        >批量删除</el-button>
      </template>
      <!-- 自定义右侧操作栏 -->
      <template slot-scope="{row,size,type,index}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-delete"
          @click="handleDelete(row)"
          v-hasPermi="['admin:local:auths:remove']"
        >删除</el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import {
  listAuths,
  addAuths,
  authRolesList,
  authFuncList,
  getAuths,
  removeAuths,
  removesAuths
} from "@/api/system/authorize";
import authorizeOption from "./infoData/authorizeOption.js";

export default {
  name: "Authorize",
  data() {
    return {
      loading: true,
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      option: authorizeOption,
      // 表单角色列表
      rolesList: [],
      funcIdList: [],
      // 非多个禁用
      multiple: true,
      //批量删除数据
      handleDeletesList: []
    };
  },
  created() {},
  methods: {
    // 分页方法，首次加载调用一次
    onLoad(page) {
      this.getList(this.search);
    },

    // 弹窗打开之前
    beforeOpen(done, type) {
      //  角色列表
      authRolesList().then(res => {
        this.rolesList = res.data;
      });
      //功能列表
      authFuncList().then(res => {
        // this.updateDictData(this.option.column, "funcId", res.data);
        this.funcIdList = res.data;
      });
      done();
    },
    // 更新数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    /** 查询用户列表 */
    getList(query) {
      const params = { ...query, ...this.page };
      this.loading = true;
      listAuths(params).then(res => {
        this.data = res.data.list;
        this.page.total = res.data.total;
        this.loading = false;
      });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
      // this.exportSearch = this.search;
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      addAuths(form).then(response => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.getList();
          done();
        } 
      });
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.handleDeletesList = [];
      selection.forEach(item => {
        const { operRoleId, authRoleId, funcId } = item;
        this.handleDeletesList.push({
          operRoleId,
          authRoleId,
          funcId
        });
      });
      this.multiple = !selection.length;
    },
    /** 批量删除操作 */
    handleDeletes() {
      this.$confirm("此操作将永久删除所选数据项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.handleDelUsers(this.handleDeletesList, removesAuths);
        })
        .catch(() => {});
    },
    async handleDelUsers(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getList();
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const operRoleId = row.operRoleId;
      this.$confirm(
        '此操作将永久删除操作角色编号为"' + operRoleId + '"的数据项,是否继续?',
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.handleDelUser(row, removeAuths);
        })
        .catch(() => {});
    },
    async handleDelUser(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getList();
      } 
    }
  }
};
</script>