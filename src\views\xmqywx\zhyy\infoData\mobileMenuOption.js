import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增系统',
    viewTitle: '查看详情',
    editTitle: '修改系统',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    // excelBtn:true,
    column: [
        {
          label: '菜单分类',
          prop: 'type',
          type: 'select',
          slot:true,
          dicData: [],
          rules: [{
            required: true,
            message: "请选择菜单分类",
            trigger: "change"
          }]
        },
        {
            label: '菜单名称',
            width: 120,
            prop: 'title',
            search: true,
            rules: [
                {
                    required: true,
                    message: '请输入菜单名称',
                    trigger: 'blur',
                },
                {
                    min: 1,
                    max: 100,
                    message: '长度在 1 到 100 个字符',
                    trigger: 'blur',
                },
            ],
        },
        {
          label: '菜单地址',
          prop: 'url',
          rules: [
            {
              required: true,
              message: '请输入菜单地址',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 500,
              message: '长度在 1 到 500 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
          ],
        },
        {
          label: 'logo图标路径',
          prop: 'image',
          rules: [
            {
              required: true,
              message: '请输入logo图标路径',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 500,
              message: '长度在 1 到 500 个字符',
              trigger: 'blur',
            },
            {
              validator: validate.blankSpace,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '排序',
          prop: 'sort',
          width: 80,
          type: 'number',
          rules: [
            {
              required: true,
              message: '请输入排序',
              trigger: 'change',
            },
          ],
        },
        {
          label: '状态',
          prop: 'status',
          type: 'select',
          slot:true,
          dicData: [],
          rules: [{
            required: true,
            message: "请选择状态",
            trigger: "change"
          }]
        },
    ],
};
