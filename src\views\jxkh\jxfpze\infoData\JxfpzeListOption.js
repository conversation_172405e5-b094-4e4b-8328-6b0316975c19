import validate from "@/utils/validate"
export default {
  index:true,
  indexLabel: '序号',
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan:4,
  searchMenuPosition: 'left',
  addTitle: '新增绩效分配',
  editTitle: '修改绩效分配',
  saveBtnText: '确定',
  editBtnText: '修改',
  updateBtnText: '确定',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  labelWidth: 100,
  columnBtn:false,
  column: [
    // {
    //   label: 'ID',
    //   prop: 'id',
    //   search: true,
    //   editDisabled: true,
    //   slot: true,
    //   rules: [{
    //     required: true,
    //     message: "角色编号不能为空",
    //     trigger: "blur"
    //   },
    //   // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
    //   // {
    //   //   validator: validate.roleIdValidate, trigger: 'blur'
    //   // }
    // ]
    // },
    {
      label: '机构编号',
      prop: 'deptId',
      slot: true,
      search: true,
      rules: [{
        required: true,
        message: "机构编号不能为空",
        trigger: "blur"
      },
     ]
        // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
      // {validator: validate.blankSpace, trigger: 'blur'}]
    },
    {
      label: "绩效类型",
      prop: "type",
      type: "radio",
      search: true,
      slot: true,
      dicData: [],
      width: 80,
      rules: [
        {
          required: true,
          message: '请选择绩效类型',
          trigger: 'change',
        },
      ],
    },
    {
      label: '年度',
      prop: 'year',
      type: "year",
      search: true,
      format:"yyyy",
      valueFormat: 'yyyy',
      rules: [{
        required: true,
        message: "年度不能为空",
        trigger: "blur"
      }]
    },
    {
      label: '季度',
      prop: 'yearQuar',
      showColumn: false,
      search: true,
      type: "select",
      dicData: [{
        label: '第一季度',
        value: '1'
      },
        {
          label: '第二季度',
          value: '2'
        },
        {
          label: '第三季度',
          value: '3'
        },
        {
          label: '第四季度',
          value: '4'
        }]
    },
    {
      label: '月份',
      prop: 'yearMonth',
      type: "month",
      search: true,
      format:"MM"
    },

    {
      label: '科目名称',
      prop: 'subjectName',
      slot: true,
      type: 'select',
      rules: [{
        required: true,
        message: "科目名称不能为空",
        trigger: "blur"
      },
      {
        validator: validate.bhOrNameValidate, trigger: 'blur'
      }]
      //checkNumber
    },
        {
          label: '科目编号',
          prop: 'bh',
          addDisplay: false,
          editDisplay: false,

          dicData: [],

          slot: true,
          rules: [{
            required: true,
            message: "科目编号",
            trigger: "blur"
          }]
        },
            {
              label: '绩效分配总额上限',
              prop: 'zesx',

              slot: true,
              rules: [{
                required: true,
                message: "绩效分配总额上限",
                trigger: "blur"
              }
              ,
                {
                  validator: validate.checkNumber, trigger: 'blur'
                }
              ]
            },
                // {
                //   label: '绩效分配总额下限',
                //   prop: 'zexx',
                //   slot: true,
                //   rules: [{
                //     required: true,
                //     message: "序号不能为空",
                //     trigger: "blur"
                //   }]
                // },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      editDisplay: false,
      addDisplay: false,
    }
      // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
      // {validator: validate.blankSpace, trigger: 'blur'


    // {
    //   label: '角色状态',
    //   prop: 'status',
    //   type: "select",
    //   slot: true,
    //   dicData: [],
    //   search: true,
    //   rules: [{
    //     required: true,
    //     message: "请选择状态",
    //     trigger: "blur,change"
    //   }]
    // },
    // {
    //   label: '角色类型',
    //   prop: 'roleType',
    //   editDisplay: false,
    //   addDisplay: false,
    //   headerslot:true,
    // },
    // {
    //   label: '创建人',
    //   prop: 'createUser',
    //   editDisplay: false,
    //   addDisplay: false,
    // },
    // {
    //   label: '创建时间',
    //   prop: 'createTime',
    //   type: 'datetime',
    //   editDisplay: false,
    //   addDisplay: false,
    // }
  ],
  // group: [{
  //   column: [
  //     {
  //       label: '角色名称',
  //       prop: 'roleName',
  //       rules: [{
  //         required: true,
  //         message: "角色名称不能为空",
  //         trigger: "blur"
  //       }]
  //     },
  //     {
  //       label: '权限字符',
  //       prop: 'roleKey',
  //       search: true,
  //       labelTip: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)",
  //       rules: [{
  //         required: true,
  //         message: "权限字符不能为空",
  //         trigger: "blur"
  //       }]
  //     },
  //     {
  //       label: '角色顺序',
  //       prop: 'roleSort',
  //       type: "number",
  //       minRows: 0,
  //       value: 0,
  //       rules: [
  //         {
  //           required: true,
  //           message: "角色顺序不能为空",
  //           trigger: "blur"
  //         }
  //       ]
  //     },
  //     {
  //       label: '状态',
  //       prop: 'status',
  //       type: 'radio',
  //       search: true,
  //       slot: true,
  //       searchslot: true,
  //       value: "0",
  //       dicData: [{
  //         label: '正常',
  //         value: "0"
  //       }, {
  //         label: '停用',
  //         value: "1"
  //       }]
  //     }, {
  //       label: '菜单权限',
  //       prop: 'menuPermission',
  //       span: 24,
  //       formslot: true
  //     }, {
  //       label: '备注',
  //       prop: 'remark',
  //       type: 'textarea',
  //       placeholder: '请输入内容',
  //       span: 24
  //     }
  //   ]
  // }]
}
