import request from '@/utils/request';

// 定时任务管理 - 列表查询;
export function list(data) {
    let url = `/api/admin/quartzJob/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新增定时任务
export function add(data) {
    return request({
        url: '/api/admin/quartzJob/add',
        method: 'post',
        data: data,
    });
}

//删除定时任务、批量删除
export function remove(ids) {
    return request({
        url: `/api/admin/quartzJob/delete?ids=${ids}`,
        method: 'get',
    });
}

// 编辑定时任务
export function edit(data) {
    return request({
        url: '/api/admin/quartzJob/edit',
        method: 'put',
        data: data,
    });
}

// 启动定时任务
export function resume(data) {
    return request({
        url: '/api/admin/quartzJob/resume',
        method: 'put',
        data: data,
    });
}

// 暂停定时任务
export function pauseJob(data) {
    return request({
        url: '/api/admin/quartzJob/pause',
        method: 'put',
        data: data,
    });
}

// 立即执行定时任务
export function immediate(data) {
    return request({
        url: '/api/admin/quartzJob/immediate',
        method: 'put',
        data: data,
    });
}

