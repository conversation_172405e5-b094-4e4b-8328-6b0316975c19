package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.BusinessQueryApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 业务查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Component
public class BusinessQueryController implements BusinessQueryApi {

    @Resource
    private IBusinessQueryService businessQueryService;

    @Override
    @PMCTLLog(name = "业务查询转发", action = "转发")
    public RestResponse<Object> businessQuery(BusinessQueryVo businessQueryVo) {
        log.info("接收到业务查询请求，账户: {}, 目标URL: {}", businessQueryVo.getAccount(), businessQueryVo.getTargetUrl());
        
        try {
            // 参数验证
            if (businessQueryVo.getTargetUrl() == null || businessQueryVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }
            
            // 执行业务查询转发
            Object result = businessQueryService.executeBusinessQuery(businessQueryVo);
            
            log.info("业务查询转发完成，账户: {}", businessQueryVo.getAccount());
            return RestResponse.success(result);
            
        } catch (Exception e) {
            log.error("业务查询转发异常，账户: {}, 错误信息: {}", businessQueryVo.getAccount(), e.getMessage(), e);
            return RestResponse.fail("业务查询转发失败: " + e.getMessage());
        }
    }
}
