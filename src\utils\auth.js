import local from '@/plugins/cache'

// 登录凭证缓存键值
const cacheKey = 'LogonCredentials'
// 统一认证令牌缓存键值
const cacheUAASTokenKey = 'UAAS-Token'
// 登录模式缓存键值
const cacheAuthMode = 'authMode'

/**
 * 设置登录模式到缓存
 * @param data
 */
export function setAuthMode(data) {
  local.local.set(cacheAuthMode, data)
}

/**
 * 从本地缓存中获取登录模式
 * @returns {string | string}
 */
export function getAuthMode() {
  return local.local.get(cacheAuthMode) || 'auth'
}

/**
 * 清除缓存登录模式
 */
export function removeAuthMode() {
  return local.local.remove(cacheAuthMode)
}

/**
 * 设置统一认证令牌到缓存
 * @param data
 */
export function setUAASToken(data) {
  local.local.set(cacheUAASTokenKey, data)
}

/**
 * 从本地缓存中获取统一认证令牌
 * @returns {null|string}
 */
export function getUAASToken() {
  return local.local.get(cacheUAASTokenKey)
}

/**
 * 清除缓存统一认证令牌
 */
export function removeUAASToken() {
  return local.local.remove(cacheUAASTokenKey)
}

/**
 * 设置登录凭证到缓存
 * @param data
 */
export function setLogonCredentials(data) {
  local.local.setJSON(cacheKey, data)
}

/**
 * 从本地缓存中获取登录凭证
 * @returns {any | undefined}
 */
export function getLogonCredentials() {
  return local.local.getJSON(cacheKey)
}

/**
 * 清除缓存登录凭证
 */
export function removeLogonCredentials() {
  return local.local.remove(cacheKey)
}

/**
 * 从本地缓存中获取访问令牌值
 * @returns {Token.value|null}
 */
export function getAccessToken() {
  return getLogonCredentials()?.accessToken?.tokenValue || null
}

/**
 * 从本地缓存中获取刷新令牌值
 * @returns {Token.value|null}
 */
export function getRefreshToken() {
  return getLogonCredentials()?.refreshToken?.tokenValue || null
}

/**
 * 从本地缓存中获取访问令牌类型
 * @returns {*|null}
 */
export function getAccessTokenType() {
  return getLogonCredentials()?.accessToken?.tokenType?.value || null
}

/**
 * 从本地缓存中获取访问令牌签发时间
 * @returns {*|null}
 */
export function getAccessTokenIssuedAt() {
  return getLogonCredentials()?.accessToken?.issuedAt || null
}

/**
 * 从本地缓存中获取刷新令牌签发时间
 * @returns {*|null}
 */
export function getRefreshTokenIssuedAt() {
  return getLogonCredentials()?.refreshToken?.issuedAt || null
}

/**
 * 从本地缓存中获取访问令牌过期时间
 * @returns {*|null}
 */
export function getAccessTokenExpiresAt() {
  return getLogonCredentials()?.accessToken?.expiresAt || null
}

/**
 * 从本地缓存中拼装Authorization头信息
 * @returns {string|null}
 */
export function getAuthorization() {
  const accessToken = getAccessToken()
  const accessTokenType = getAccessTokenType()
  if (accessToken && accessTokenType) {
    // Authorization: Bearer eyJ0eXAiO.eyJkZQxMTUyNjwv0.yHwsHgFldNspw
    return accessTokenType + ' ' + accessToken
  } else {
    return null
  }
}

/**
 * 判断缓存访问令牌是否过期
 * @returns {boolean}
 */
export function accessTokenIsExpired() {
  const expiresAt = getAccessTokenExpiresAt()
  if (expiresAt) {
    return new Date(expiresAt).getTime() > Date.now().getTime()
  }
  return false
}
