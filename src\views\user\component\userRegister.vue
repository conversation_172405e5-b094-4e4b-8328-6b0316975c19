<template>
  <!-- 授权用户 -->
  <el-dialog
    :visible.sync="visible"
    append-to-body
    :style="styleName"
    width="60%"
    :class="fullscreen?'yo-dialog yo-table__dialog yo-dialog--fullscreen':'yo-dialog '"
  >
    <div slot="title" class="yo-table__dialog__header">
      <span class="el-dialog__title">用户注册</span>
      <div class="yo-table__dialog__menu">
        <i
          @click="handleFullScreen"
          :class="fullscreen?'el-icon-news':'el-icon-full-screen'"
          class="el-dialog__close"
        ></i>
      </div>
    </div>
    <yo-table
      v-loading="loading"
      ref="crud"
      :data="instList"
      :option="option"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:teller:unregister:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
      </template>
      <!-- 自定义列 -->
      <template slot="personcode" slot-scope="scope">
        <div>
          <span>{{scope.row.personcode}}</span>
          <!-- <el-button
            type="text"
            icon="el-icon-view"
            @click="viewUserInfo(scope.row,'personcode')"
          ></el-button>-->
        </div>
      </template>
      <template slot="phone" slot-scope="scope">
        <div>
          <span>{{scope.row.phone}}</span>
          <!-- <el-button
            type="text"
            icon="el-icon-view"
            @click="viewUserInfo(scope.row,'phone')"
          ></el-button>-->
        </div>
      </template>
    </yo-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :size="option.size || 'small'" @click="handleSelectUser">保 存</el-button>
      <el-button :size="option.size || 'small'" @click="cancelSelectUser">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { userUnregisterInfo, registerUser } from "@/api/system/user";

export default {
  dicts: ["yoaf_teller_sex"],
  data() {
    return {
      formParent: {},
      fullscreen: false,
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1
      },
      option: {
        selection: true,
        align: "center",
        card: true,
        menuAlign: "center",
        emptyBtnText: "重置",
        emptyBtnIcon: "el-icon-refresh",
        searchMenuSpan: 8,
        menu: false,
        searchBtn: false,
        emptyBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        columnBtn: false,
        refreshBtn: false,
        tip: false,
        searchShowBtn: false,
        column: [
          {
            label: "用户名",
            prop: "chgTellerNo",
            search: true
          },
          {
            label: "姓名",
            prop: "tellerName",
            search: true
          },
          {
            label: "所属机构编号",
            prop: "tellerInst"
          },
          {
            label: "性别",
            prop: "sex",
            type: "select",
            dicData: []
          },
          {
            label: "身份证号",
            prop: "personcode",
            slot: true,
            width: 170
          },
          {
            label: "手机号",
            prop: "phone",
            slot: true,
            width: 120
          }
        ]
      },
      // 遮罩层
      visible: false,
      // 选中数组值
      instIds: [],
      // 授权用户数据
      instList: []
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: "15%", bottom: "5%" };
      } else {
        return { top: 0, bottom: 0, marginTop: 0 };
      }
    }
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "sex",
      this.dict.type["yoaf_teller_sex"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      setTimeout(() => {
        this.getList();
      }, 500);
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 显示弹框
    show() {
      this.getList();
      setTimeout(() => {
        this.visible = true;
        this.fullscreen = false;
      }, 500);
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 点击搜索
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.instIds = selection;
    },
    // 查询表数据
    getList(params) {
      this.loading = true;
      userUnregisterInfo({ ...params, ...this.page }).then(res => {
        const data = res.data;
        this.instList = data.list;
        this.page.total = data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 选择机构保存 */
    handleSelectUser() {
      if (this.instIds == []) {
        this.$modal.msgError("请选择要注册的用户");
        return;
      }
      registerUser(this.instIds).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.visible = false;
          this.$emit("ok");
        }
      });
    },
    /**敏感信息显隐**/
    viewUserInfo(data, type) {
      let sensitiveStatus = data[type].includes("**") ? "1" : "2";
      usersSensitive(data.chgTellerNo, sensitiveStatus).then(response => {
        const res = response.data;
        this.data.forEach(item => {
          if (item.chgTellerNo == data.chgTellerNo) {
            item[type] = res[type];
          }
        });
      });
    },
    cancelSelectUser() {
      this.visible = false;
      this.fullscreen = false;
    }
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
</style>