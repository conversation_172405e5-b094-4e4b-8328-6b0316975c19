import validate from "@/utils/validate";

export default {
  index: true,
  indexLabel: "序号",
  // selection: true,
  align: "center",
  card: true,
  // searchSpan: 6,
  searchMenuSpan: 6,
  // searchMenuPosition: 'left',
  // searchLabelWidth: 10,
  addTitle: "新增部门",
  editTitle: "修改部门",
  updateBtnText: "保存",
  selection: true,
  tip: false,
  rowKey: "deptId",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  emptyBtn: false,
  refreshBtn: false,
  labelPosition: "right",
  labelWidth: 120,
  maxHeight: 450,
  tip: false,
  columnBtn: false,
  column: [
    {
      label: "部门编号",
      prop: "deptId",
      search: true,
      editDisabled: true,
      rules: [
        {
          required: true,
          message: "请输入部门编号",
          trigger: "blur"
        },
        { min: 1, max: 12, message: "长度在 1 到 12 个字符", trigger: "blur" },
        { validator: validate.roleIdValidate, trigger: 'blur'}
      ]
    },
    {
      label: "部门名称",
      prop: "deptName",
      search: true,
      showColumn: false,
      rules: [
        {
          required: true,
          message: "请输入部门名称",
          trigger: "blur"
        },
        {
          min: 1,
          max: 64,
          message: "长度在 1 到 64 个字符",
          trigger: "blur"
        },
        {
          validator: validate.blankSpace,
          trigger: "blur"
        }
      ]
    },
    {
      label: "上级机构/部门",
      prop: "upDeptNameA",
      formslot:true,
      search: false,
      editDisplay:false,
      viewDisplay:false,
      rules: [
        {
          required: true,
          message: "请选择上级机构/部门",
          trigger: "blur"
        }
      ]
    },
    {
      label: '上级机构/部门',
      prop: 'upDeptNameB',
      hide: true,
      editDisabled: true,
      addDisplay: false,
    },
    {
      label: "状态",
      prop: "deptStatus",
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      rules: [
        {
          required: true,
          message: "请选择状态",
          trigger: "blur,change"
        }
      ]
    },
    {
      label: "显示排序",
      prop: "displayOrder",
      viewDisplay: false,
      hide: true,
      type: 'number',
      minRows: 0,
      maxRows: 10000,
      rules: [
        {
          required: true,
          message: "请输入排序",
          trigger: "blur"
        }
      ]
    },
    {
      label: "创建人",
      prop: "createUser",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false
    }
  ]
};
