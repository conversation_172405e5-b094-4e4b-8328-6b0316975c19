<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @row-update="rowUpdate" @row-save="rowSave" @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>
      <!-- 自定义列 -->
      <template slot="updateTimeSearch">
        <el-date-picker v-model="search.updateTimeBox" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="截止日期" value-format="yyyyMMddHHmmss"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </template>

      <!-- 自定义左侧操作栏 -->
      <template slot="menuLeft">

        <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="handleAdd">新增</el-button>
        <!-- <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">批量删除</el-button> -->
      </template>
      <!-- 自定义右侧操作栏 -->
      <template slot="menuRight">
        <input id="fileslist" v-show="false" type="file" accept=".xls" ref="fileRef" @change="fileChange" />
        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </template>

      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-edit" @click.stop="editJgzh(row,index)">
          修改
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-delete" @click="handleDelete(row)">
          删除
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          查看
        </el-button>
      </template>
    </yo-table>

    <!-- <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
      <span slot="footer" class="dialog-footer">
        <el-button style="margin-right: 15px" type="primary" @click="dialogQueren(dialogType)" size="mini">
          确 定
        </el-button>
        <el-button @click="dialogVisible = false" size="mini">
          取 消
        </el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
import {
  addJgzhInfo,
  list,
  exportJgzh,
  downloadExample,
  importJgzh,
  importJgzhAdd,
  editJgzh,
  viewJgzh,
  batchUpdateJgzh,
  delJgzh,
  delJgzhs
} from '@/api/jgzh';
import jgzhOption from './infoData/jgzhDtlOption.js';
import Cookies from "js-cookie";

export default {
  name: 'jgzh',
  dicts: ['jgzh_supp_mark', 'jgzh_stock_status', 'jgzh_ple_status'],
  components: {},
  data () {
    return {
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: jgzhOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    'search.updateTimeBox': {
      handler (val) {
        if (val) {
          this.search.updateTimeStart = val[0];
          this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    // 新增表单保存
    async rowSave (form, done, loading) {
      console.log("新增表单参数==>" + JSON.stringify(form))
      addJgzhInfo(form).then(res => {
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await list(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.id).join(',');
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },

    /** 新增按钮操作 */
    handleAdd () {
      this.deptName = '';
      this.instName = '';
      this.$refs.crud.rowAdd();
    },

    /** 导出结果，处理返回的流文件 */
    handleExportData (res) {
      console.log('res:', res);
      if (!res) return;
      let data = res.data;
      let filename = res.headers['content-disposition'].split('=')[1];
      let _filename = decodeURI(filename);
      const link = document.createElement('a');
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' });
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 下载模板 */
    exportExample () {
      downloadExample().then(res => {
        this.handleExportData(res);
      });
    },
    /** 导出按钮操作 */
    handleExport () {
      const data = { userIds: this.ids, ...this.exportSearch };
      // console.log('导出数据：', this.search);
      exportJgzh(this.search).then(res => {
        // console.log('导出的返回数据：', res);
        this.handleExportData(res);
      });
    },

    fileChange (event) {
      console.log("文件变化filechange--" + JSON.stringify(event));
      this.loading = true;
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append('file', file);
      console.log("按钮属性---" + this.buttonAttr);
      if (this.buttonAttr == '0') {
        console.log("全量导入---->");
        importJgzh(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("全量导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("全量导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else if (this.buttonAttr == '1') {
        console.log("增量导入---->");
        importJgzhAdd(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("增量导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("增量导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else {
        console.log("未知按钮属性，无导入操作");
      }
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport (event) {
      console.log("handleImport--" + event);
      this.buttonAttr = event;
      if (this.buttonAttr == '0') { //全量导入
        this.$confirm("此操作将会清空表数据，确认是否全量导入？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});
      } else if (this.buttonAttr == '1') { //增量导入
        this.$confirm("此操作只新增和更新部分数据，确认是否增量导入？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});

      } else {
        console.log("异常情况，未获取到按钮事件类型:" + this.buttonAttr);
      }


    },
    //过滤列表数据
    filterData (data) {
      return data;
    },
    /*
        dialogQueren (type) {
          if (type == 'dept') {
            let arr = this.$refs.tree.getCheckedNodes();
            let deptName = arr.map(i => {
              return i.name;
            });
            this.deptName = deptName.join(',');
            let deptIds = arr.map(i => {
              return i.id;
            });
            this.formParent.deptIds = deptIds;
          } else if (type == 'auth') {
            this.queryList();
          } else {
            let arr = this.$refs.tree.getCheckedNodes();
            if (!arr.length) {
              this.instName = '';
              this.formParent.instId = '';
            } else {
              this.instName = arr[0].name;
              this.formParent.instId = arr[0].id;
            }
          }
          this.dialogVisible = false;
        }, */
    /*  tagType (row) {
        switch (row.userStat) {
          case '01':
            return 'success';
          case '02':
            return 'danger';
          case '03':
            return 'info';
          case '04':
            return 'warning';
          default:
            break;
        }
      }, */
    // 修改
    async editJgzh (row, index) {
      var res = {};
      res.id = row.id
      res.stockNum = row.stockNum;
      res.othWarrantNum = row.othWarrantNum;
      res.stockStatus = row.stockStatus;
      res.suppRegistMark = row.suppRegistMark;
      res.pledgeStatusAfterlogout = row.pledgeStatusAfterlogout;
      res.noteInfo = row.noteInfo;
      res.propertycardNum = row.propertycardNum;
      res.undetermined = row.undetermined;
      Cookies.set("stockStatus", row.stockStatus);
      Cookies.set("suppRegistMark", row.suppRegistMark);
      Cookies.set("pledgeStatusAfterlogout", row.pledgeStatusAfterlogout);
      Cookies.set("noteInfo", row.noteInfo);
      Cookies.set("propertycardNum", row.propertycardNum);
      Cookies.set("undetermined", row.undetermined);
      this.$refs.crud.rowEdit(res, index);
    },
    //查看
    async handleView (row) {
      var id = row.id;
      viewJgzh(id).then(res => {
        console.log("查询返回的结果--" + JSON.stringify(res))
        if (res.code == 0) {
          this.$refs.crud.rowView(res.data);
        }
      });
    },

    // 修改表单保存
    rowUpdate (form, index, done, loading) {
      /* if (form.stockStatus == Cookies.get("stockStatus")) {
         delete form.stockStatus;
       }
       if (form.suppRegistMark == Cookies.get("suppRegistMark")) {
         delete form.suppRegistMark;
       }
       if (form.pledgeStatusAfterlogout == Cookies.get("pledgeStatusAfterlogout")) {
         delete form.pledgeStatusAfterlogout;
       } */
      editJgzh(form).then(response => {
        done()
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
        }
      });
    },

    //批量修改
    async batchUpdateJgzh (row) {
      const h = this.$createElement;
      this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h('p', { style: 'word-break: break-all' },
              '是否批量修改选中数据状态？'
            ),
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
        .then(async () => {
          let res;
          if (row.id != undefined) {
            res = await batchUpdateJgzh({ id: row.id });
          } else {
            if (
              this.ids != null &&
              this.ids != '' &&
              this.ids != undefined
            ) {
              res = await batchUpdateJgzh({ id: this.ids });
            }
          }
          this.getList(this.search);
        })
        .catch(() => {});
    },

    /** 删除按钮操作 */
    handleDelete (row) {
      const id = row.id || this.id;
      const stockNum = row.stockNum;
      const othWarrantNum = row.othWarrantNum;
      const h = this.$createElement;
      this.$confirm(`是否永久删除id为" ${id} ",库存序号为" ${stockNum} "，他项权证号为" ${othWarrantNum} "的数据项？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          showCancelButton: true,
        }).then(async () => {
          // 删除单条记录
          if (row.id) {
            console.log("删除单个监管账户信息----")
            this.handleDelJgzh(id, delJgzh);
          } else {
            console.log("批量删除监管账户信息----")
            this.handleDelJgzh(id, delJgzhs);
          }
        })
        .catch(() => {});
    },

    async handleDelJgzh (id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.$refs.crud.toggleSelection();
        this.getList();
      }
    },
    // end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}
</style>
