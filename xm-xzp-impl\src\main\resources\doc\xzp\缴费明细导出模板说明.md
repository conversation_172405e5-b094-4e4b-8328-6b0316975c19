# 缴费明细导出模板创建说明

## 模板文件名
缴费明细导出模板.xls

## 模板结构

### 表头（第1行）
| 日期 | 业务代码_商户号 | 子业务代码 | 业务名称 | 笔数 | 金额 |

### 数据行模板（第2行）
使用EasyPOI的模板语法：
| {{t.setdate}} | {{t.str}} | {{t.str30}} | {{t.str31}} | {{t.num}} | {{t.amt}} |

### EasyPOI配置
- 数据集合名称：txnLogDetailList
- 模板变量前缀：t
- 循环标记：fe:txnLogDetailList t

## 创建步骤
1. 使用Excel创建一个新的工作簿
2. 在第一行创建表头，包含上述6个列
3. 在第二行添加EasyPOI的模板标记
4. 设置列宽和格式
5. 保存为.xls格式（注意不是.xlsx）
6. 将文件放置在 xm-xzp-impl/src/main/resources/doc/xzp/ 目录下

## 注意事项
- 文件必须是.xls格式，不能是.xlsx
- 模板变量名必须与后端Map中的key对应
- 金额字段可能需要除以100来转换为元单位
- 日期格式需要根据实际需求调整

## 后端代码对应
```java
Map<String, Object> map = new HashMap<>();
map.put("txnLogDetailList", txnLogDetailList);
```

数据字段对应：
- setdate: 日期
- str: 业务代码_商户号
- str30: 子业务代码  
- str31: 业务名称
- num: 笔数
- amt: 金额
