package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.psbc.pfpj.yoaf.response.util.ResponseUtils;
import com.xm.xzp.api.IntTxnLogApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import com.xm.xzp.service.IIntTxnLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@Component
public class IntTxnLogController implements IntTxnLogApi {

    @Resource
    private IIntTxnLogService intTxnLogService;

    @Override
    @PMCTLLog(name = "查询交易流水", action = "query")
    public RestResponse<PageInfo<IntTxnLog>> intTxnLogList(IntTxnLogVo intTxnLog,
                                                          Integer pageNum, Integer pageSize) {
        PageInfo<IntTxnLog> pageList = intTxnLogService.intTxnLogList(
                intTxnLog, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "查询交易流水", action = "query")
    public RestResponse<PageInfo<IntTxnLogResultVo>> selectIntTxnLogList(IntTxnLogVo intTxnLog,
                                                           Integer pageNum, Integer pageSize) {
        PageInfo<IntTxnLogResultVo> pageList = intTxnLogService.selectIntTxnLogList(
                intTxnLog, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "缴费明细查询", action = "query")
    public RestResponse<PageInfo<Object>> queryTxnLogDetail(IntTxnLogVo intTxnLog,
                                                           Integer pageNum, Integer pageSize) {
        PageInfo<Object> pageList = intTxnLogService.queryTxnLogDetail(
                intTxnLog, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "导出缴费明细", action = "export")
    public void exportTxnLogDetail(IntTxnLogVo intTxnLog, HttpServletResponse response) throws IOException {
        Workbook workbook = intTxnLogService.exportTxnLogDetail(intTxnLog);
        ResponseUtils.responseExcel(response, "缴费明细列表.xls", workbook);
    }
}