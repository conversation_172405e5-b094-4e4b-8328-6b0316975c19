# 状态下拉框修复说明

## 问题分析

状态下拉框数据没有出来的原因是配置方式不符合项目标准。

## 标准实现方式

通过查看`src\views\xzp`下其他页面的实现方式，发现项目使用的标准字典下拉框实现模式：

### 1. 组件声明
```javascript
export default {
  name: 'ComponentName',
  dicts: ['字典类型名称'], // 声明需要使用的字典类型
  // ...
}
```

### 2. 配置文件设置
```javascript
{
  label: '状态',
  prop: 'fieldName',
  type: 'select',
  dicData: [], // 空数组，将通过updateDictData方法动态更新
  // 其他配置...
}
```

### 3. 生命周期初始化
```javascript
created() {
  // 更新字典数据
  this.updateDictData(this.option.column, 'fieldName', this.dict.type.字典类型名称);
}
```

### 4. 工具方法
```javascript
methods: {
  // 更新字典数据
  updateDictData(option, key, data) {
    const column = this.findObject(option, key);
    if (column && data) {
      column.dicData = data;
    }
  },
  
  // 查找对象
  findObject(array, prop) {
    return array.find(item => item.prop === prop);
  }
}
```

## 参考页面

### supAcctPayInst页面
- **文件**: `src\views\xzp\supAcctPayInst\index.vue`
- **字典声明**: `dicts: ['xzp_vch_type','xzp_tran_fg']`
- **配置**: `dicData: []`
- **初始化**: 
  ```javascript
  this.updateDictData(this.option.column, 'vchType', this.dict.type.xzp_vch_type);
  this.updateDictData(this.option.column, 'tranFg', this.dict.type.xzp_tran_fg);
  ```

### batchtranctrl页面
- **文件**: `src\views\xzp\batchtranctrl\index.vue`
- **字典声明**: `dicts: ['xzp_ans_state']`
- **配置**: `dicData: []`
- **初始化**: 
  ```javascript
  this.updateDictData(this.option.column, 'procFg', this.dict.type.xzp_ans_state);
  ```

## 修改内容

### 1. 配置文件修改
**文件**: `src\views\xzp\acctmgmt\infoData\acctMgmtOption.js`

**修改前**:
```javascript
dicData: [
  { label: '正常', value: '0' },
  { label: '已删除', value: '1' }
],
dicUrl: '/api/admin/sys/dict/actions/get/xzp_acct_status',
dicMethod: 'get',
props: {
  label: 'dictName',
  value: 'dictId'
}
```

**修改后**:
```javascript
dicData: [], // 将通过updateDictData方法动态更新
```

### 2. 组件文件修改
**文件**: `src\views\xzp\acctmgmt\index.vue`

**已有配置**:
- ✅ 字典声明: `dicts: ['xzp_acct_status']`
- ✅ created方法: `this.updateDictData(this.option.column, 'othMsg1Tx', this.dict.type.xzp_acct_status);`
- ✅ updateDictData方法
- ✅ findObject方法

## 工作原理

1. **字典声明**: 组件启动时，Vue字典插件会根据`dicts`数组自动加载对应的字典数据
2. **数据存储**: 字典数据存储在`this.dict.type.字典类型名称`中
3. **动态更新**: 在`created()`生命周期中，通过`updateDictData()`方法将字典数据赋值给配置中的`dicData`
4. **渲染显示**: yo-table组件读取`dicData`数组渲染下拉选项

## 字典数据格式

字典数据的标准格式：
```javascript
[
  {
    dictTypeId: "xzp_acct_status",
    dictId: "0", 
    dictName: "正常",
    status: "1",
    displayOrder: 1
  },
  {
    dictTypeId: "xzp_acct_status",
    dictId: "1",
    dictName: "已删除", 
    status: "1",
    displayOrder: 2
  }
]
```

## 注意事项

1. **字典类型名称**: 必须与数据库中的`dict_type_id`完全一致
2. **字段映射**: 组件会自动将`dictId`映射为`value`，`dictName`映射为`label`
3. **数据状态**: 只有`status`为'1'的字典项才会被加载
4. **加载时机**: 字典数据在组件创建时异步加载，需要在`created()`中进行初始化

## 测试方法

1. **检查字典声明**: 确认`dicts`数组包含正确的字典类型
2. **检查数据库**: 确认字典数据存在且状态正确
3. **检查控制台**: 查看是否有字典加载相关的错误信息
4. **检查组件状态**: 在Vue DevTools中查看`this.dict.type.xzp_acct_status`是否有数据

## 预期结果

修改完成后，状态下拉框应该能正常显示"正常"和"已删除"两个选项，并且可以正常进行筛选查询。
