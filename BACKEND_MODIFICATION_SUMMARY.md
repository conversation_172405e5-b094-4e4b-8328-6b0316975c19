# 后端接口修改总结

## 修改目标
将原本使用 `tranTime`、`opeCd`、`merchId` 作为唯一值的查询逻辑改为使用 `oth_msg2_tx` 作为唯一值，并在新增时为该值添加 UUID。

## 修改文件列表

### 1. API接口定义 (`xm-xzp-api/src/main/java/com/xm/xzp/api/AcctInfoApi.java`)

**修改内容：**
- 修改 `getAcctSensitiveInfoByBizKey` 接口，参数从 `(tranTime, opeCd, merchId, sensitiveStatus)` 改为 `(othMsg2Tx, sensitiveStatus)`
- 新增 `getAcctInfoByOthMsg2Tx` 接口 - 根据 `oth_msg2_tx` 获取账户详情
- 新增 `updateAcctInfoByOthMsg2Tx` 接口 - 根据 `oth_msg2_tx` 修改账户信息
- 新增 `deleteAcctInfoByOthMsg2Tx` 接口 - 根据 `oth_msg2_tx` 删除账户信息

**新增接口：**
```java
@GetMapping("/getAcctInfoByOthMsg2Tx/{othMsg2Tx}")
RestResponse<TbZjjgAcctInfo> getAcctInfoByOthMsg2Tx(@PathVariable("othMsg2Tx") String othMsg2Tx);

@PostMapping("/updateAcctInfoByOthMsg2Tx")
RestResponse<String> updateAcctInfoByOthMsg2Tx(@RequestBody @Valid TbZjjgAcctInfo acctInfo);

@PostMapping("/deleteAcctInfoByOthMsg2Tx")
RestResponse<String> deleteAcctInfoByOthMsg2Tx(@RequestParam("othMsg2Tx") String othMsg2Tx);

@GetMapping("/acctSensitiveByBizKey/{othMsg2Tx}/{sensitiveStatus}")
RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(@PathVariable("othMsg2Tx") String othMsg2Tx, @PathVariable("sensitiveStatus") String sensitiveStatus);
```

### 2. Controller实现 (`xm-xzp-impl/src/main/java/com/xm/xzp/controller/AcctInfoController.java`)

**修改内容：**
- 实现新增的4个接口方法
- 修改 `getAcctSensitiveInfoByBizKey` 方法实现
- 添加完整的参数验证和错误处理
- 添加操作日志记录（使用 `@PMCTLLog` 注解）

**关键实现：**
```java
@Override
public RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(String othMsg2Tx, String sensitiveStatus) {
    TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctSensitiveInfoByOthMsg2Tx(othMsg2Tx, sensitiveStatus);
    // ... 错误处理和日志记录
}
```

### 3. Service接口 (`xm-xzp-impl/src/main/java/com/xm/xzp/service/ITbZjjgAcctInfoService.java`)

**修改内容：**
- 新增 `getAcctSensitiveInfoByOthMsg2Tx` 方法
- 新增 `getAcctInfoByOthMsg2Tx` 方法
- 新增 `updateAcctInfoByOthMsg2Tx` 方法
- 新增 `deleteAcctInfoByOthMsg2Tx` 方法
- 保留原有的 `getAcctSensitiveInfoByBizKey` 方法作为兼容

### 4. Service实现 (`xm-xzp-impl/src/main/java/com/xm/xzp/service/impl/TbZjjgAcctInfoServiceImpl.java`)

**主要修改：**

#### 4.1 修改新增方法 `addAcctInfo`
```java
// 为新增记录生成唯一标识（如果未设置）
if (!StringUtils.hasText(acctInfo.getOthMsg2Tx())) {
    acctInfo.setOthMsg2Tx(java.util.UUID.randomUUID().toString());
    log.info("为新增记录生成唯一标识：{}", acctInfo.getOthMsg2Tx());
}
```

#### 4.2 新增基于 `oth_msg2_tx` 的查询方法
```java
@Override
public TbZjjgAcctInfo getAcctInfoByOthMsg2Tx(String othMsg2Tx) {
    return this.lambdaQuery()
            .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
            .one();
}
```

#### 4.3 新增基于 `oth_msg2_tx` 的更新方法
```java
@Override
public boolean updateAcctInfoByOthMsg2Tx(TbZjjgAcctInfo acctInfo) {
    // 检查记录存在性和删除状态
    // 使用 oth_msg2_tx 作为更新条件
    return this.lambdaUpdate()
            .eq(TbZjjgAcctInfo::getOthMsg2Tx, acctInfo.getOthMsg2Tx())
            .update(acctInfo);
}
```

#### 4.4 新增基于 `oth_msg2_tx` 的删除方法
```java
@Override
public boolean deleteAcctInfoByOthMsg2Tx(String othMsg2Tx) {
    // 逻辑删除：设置 othMsg1Tx 为 "1"
    return this.lambdaUpdate()
            .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
            .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
            .update();
}
```

#### 4.5 新增基于 `oth_msg2_tx` 的敏感信息方法
```java
@Override
public TbZjjgAcctInfo getAcctSensitiveInfoByOthMsg2Tx(String othMsg2Tx, String sensitiveStatus) {
    // 根据 oth_msg2_tx 查询原始数据
    // 根据 sensitiveStatus 进行脱敏处理
    // 返回处理后的数据
}
```

## 兼容性处理

### 1. 接口兼容性
- 保留原有的 `getAcctSensitiveInfoByBizKey(tranTime, opeCd, merchId, sensitiveStatus)` 方法
- 新接口使用不同的URL路径，不影响现有调用

### 2. 数据兼容性
- 新增记录自动生成 `oth_msg2_tx` UUID
- 旧数据的 `oth_msg2_tx` 字段可能为空，需要数据迁移
- 所有新方法都包含空值检查和错误处理

### 3. 业务逻辑兼容性
- 保持原有的逻辑删除机制（使用 `othMsg1Tx` 字段）
- 保持原有的敏感信息脱敏逻辑
- 保持原有的参数验证和错误处理

## 数据库要求

### 1. 字段验证
- `oth_msg2_tx` 字段已在实体类中定义
- 字段类型：VARCHAR(255)
- 建议添加唯一索引以提高查询性能

### 2. 数据迁移建议
```sql
-- 为现有数据生成UUID（示例）
UPDATE tb_zjjg_acct_info 
SET oth_msg2_tx = UUID() 
WHERE oth_msg2_tx IS NULL OR oth_msg2_tx = '';

-- 添加唯一索引
CREATE UNIQUE INDEX idx_oth_msg2_tx ON tb_zjjg_acct_info(oth_msg2_tx);
```

## 测试要点

### 1. 新增功能测试
- 验证新增记录时是否自动生成UUID
- 验证UUID格式和唯一性

### 2. CRUD操作测试
- 测试基于 `oth_msg2_tx` 的查询、修改、删除功能
- 测试敏感信息显隐功能

### 3. 兼容性测试
- 测试旧数据（无 `oth_msg2_tx`）的处理
- 测试原有接口是否仍然正常工作

### 4. 异常处理测试
- 测试无效 `oth_msg2_tx` 的处理
- 测试已删除记录的操作限制
- 测试参数验证和错误提示

## 部署注意事项

1. **数据库迁移**：先执行数据库迁移脚本，为现有数据生成UUID
2. **后端部署**：部署新的后端代码
3. **前端部署**：部署修改后的前端代码
4. **功能验证**：验证新旧功能都正常工作
5. **性能监控**：监控基于UUID查询的性能表现
