import validate from "@/utils/validate";
export default {
  index: true,
  indexLabel: "序号",
  selection: true,
  align: "center",
  card: true,
  menuAlign: "center",
  emptyBtnIcon: "el-icon-refresh",
  searchMenuSpan: 6,
  searchMenuPosition: "left",
  addTitle: "黑白名单新增",
  editTitle: "黑白名单编辑",
  editBtnText: "修改",
  updateBtnText: "保存",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: "right",
  labelWidth: 120,
  tip: false,
  columnBtn: false,
  rowKey: "id",
  column: [
    {
      label: "名单类型",
      prop: "listType",
      type: "select",
      search: true,
      slot: false,
      searchslot: false,
      dicData: [
       
      ],
      rules: [
        {
          required: true,
          message: "请选择名单类型",
          trigger: "blur,change"
        }
      ]
    },
    {
      label: "起始IP",
      prop: "ipStart",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入起始IP",
          trigger: "blur"
        },
        { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
      ]
    },
    {
      label: "截止IP",
      prop: "ipEnd",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入截止IP",
          trigger: "blur"
        },
        { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
      ]
    },

    {
      label: "状态",
      prop: "status",
      type: "select",
      dicData: [],
      rules: [
        {
          required: true,
          message: "请选择状态",
          trigger: "blur,change"
        }
      ]
    },
    {
      label: "创建人",
      prop: "createUser",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "创建时间",
      prop: "id",
      hide: true,
      showColumn: false,
      editDisplay: false,
      addDisplay: false
    }
  ]
};
