import validate from "@/utils/validate"
export default {
  index:true,
  indexLabel:'序号',
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '添加用户',
  editTitle: '修改用户',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  tip:false,
  columnBtn:false,
  // excelBtn:true,
  column: [
    {
      label: '用户名',
      prop: 'userName',
      search: true,
      editDisabled: true,
      rules: [{
        required: true,
        message: "请输入用户名",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      {
        validator: validate.userIdValidate, trigger: 'blur'
      }]
    },
    {
        label: '姓名',
        prop: 'userNickname',
       
        addDisplay: false,
        editDisplay:false
      },
    
    {
      label: 'IP',
      prop: 'ip',
      
      showColumn: false,
      addDisplay: false,
      editDisplay:false
    },
    {
        label: '设备',
        prop: 'device',
       
       
        addDisplay: false,
        editDisplay:false
      },
      {
        label: '浏览器',
        prop: 'browser',
    
     
        addDisplay: false,
        editDisplay:false
      },
      {
        label: '操作系统',
        prop: 'os',
       
       
        addDisplay: false,
        editDisplay:false
      },
      {
        label: '登录时间',
        prop: 'loginTime',
   
     
        addDisplay: false,
        editDisplay:false
      },
 
    
  ]
}