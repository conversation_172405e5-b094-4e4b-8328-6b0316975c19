import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'id',
    reserveSelection: false,
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '保存',
    addTitle: '新市民查询',
    addBtn: false,
    // addBtnText: '新市民查询',
    addBtnIcon: 'el-icon-search',
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchShowBtn: false,
    searchRow: false,
    labelWidth: 120,
    dialogWidth: 800,
    columnBtn: false,
    excelBtn: false,
    menuWidth: 100,
    menu: false,
    column: [
        {
            label: '序列号',
            prop: 'seqNo',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            width: 160,
        },
        {
            label: '身份证号码',
            prop: 'idCard',
            searchSpan: 8,
            search: false,
            searchLabelWidth: 90,
            width: 160,
        },
        {
            label: '查询人员',
            prop: 'createUser',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            search: true,
        },
        {
            label: '查询时间',
            prop: 'createTime',
            showColumn: false,
            search: true,
            searchslot: true,
            addDisplay: false,
            editDisplay: false,
            width: 160,
        },

        {
            label: '授权人员',
            prop: 'authorizeUser',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '响应状态',
            prop: 'status',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            search: true,
            type: 'select',
            dicData: [],
        },
        {
            label: '响应时间',
            prop: 'resTime',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            width: 160,
        },
        {
            label: '查询类型',
            prop: 'queryType',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            search: true,
            type: 'select',
            dicData: [],
        },
        {
            label: '分数',
            prop: 'score',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
    ],
};
