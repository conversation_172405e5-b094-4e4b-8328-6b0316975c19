-- 调试SQL脚本 - 用于排查逻辑删除问题

-- 1. 查看表中所有数据的状态分布
SELECT 
    CASE 
        WHEN oth_msg1_tx IS NULL THEN 'NULL'
        WHEN oth_msg1_tx = '' THEN 'EMPTY'
        WHEN oth_msg1_tx = '0' THEN 'ZERO'
        WHEN oth_msg1_tx = '1' THEN 'ONE'
        ELSE oth_msg1_tx
    END as status_value,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
GROUP BY oth_msg1_tx
ORDER BY count DESC;

-- 2. 查看前10条记录的详细信息
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        WHEN oth_msg1_tx IS NULL OR oth_msg1_tx != '1' THEN '正常'
        ELSE '未知状态:' || oth_msg1_tx
    END as status_desc,
    tran_dt,
    tran_time
FROM public.tb_zjjg_acct_info
ORDER BY cpab_acc_id, acct_nm
LIMIT 10;

-- 3. 测试查询条件 - 模拟前端传入 othMsg1Tx = '1'
SELECT COUNT(*) as deleted_count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1';

-- 4. 测试查询条件 - 模拟前端传入 othMsg1Tx = '0'
SELECT COUNT(*) as normal_count
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1');

-- 5. 测试查询条件 - 默认查询（不传状态）
SELECT COUNT(*) as default_count
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1');

-- 6. 手动创建一条测试数据（已删除状态）
-- 注意：请根据实际情况修改cpab_acc_id和acct_nm
/*
UPDATE public.tb_zjjg_acct_info 
SET oth_msg1_tx = '1' 
WHERE cpab_acc_id = '你的测试账户ID' 
  AND acct_nm = '你的测试账户名'
  AND ROWNUM = 1;
*/

-- 7. 验证更新是否成功
/*
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx,
    CASE 
        WHEN oth_msg1_tx = '1' THEN '已删除'
        ELSE '正常'
    END as status_desc
FROM public.tb_zjjg_acct_info
WHERE cpab_acc_id = '你的测试账户ID' 
  AND acct_nm = '你的测试账户名';
*/

-- 8. 查找可以用于测试的记录
SELECT 
    cpab_acc_id,
    acct_nm,
    oth_msg1_tx
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
ORDER BY cpab_acc_id, acct_nm
LIMIT 5;

-- 9. 测试MyBatis条件 - 验证 (oth_msg1_tx IS NULL OR oth_msg1_tx != '1') 的逻辑
SELECT 
    'NULL值测试' as test_type,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx IS NULL

UNION ALL

SELECT 
    '非1值测试' as test_type,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx IS NOT NULL AND oth_msg1_tx != '1'

UNION ALL

SELECT 
    '等于1测试' as test_type,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
WHERE oth_msg1_tx = '1'

UNION ALL

SELECT 
    '组合条件测试' as test_type,
    COUNT(*) as count
FROM public.tb_zjjg_acct_info
WHERE (oth_msg1_tx IS NULL OR oth_msg1_tx != '1');
