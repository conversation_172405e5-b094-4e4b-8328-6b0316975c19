<!--批量交易控制表-->
<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" class="search-form" label-width="100px" size="small">
      <el-row :gutter="24">
        <el-col :span="10" style="display: flex;align-items: center;">
          <el-form-item label="业务代码">
            <el-autocomplete
              v-model="search.opeCd"
              :fetch-suggestions="queryOpeCd"
              placeholder="请输入业务代码"
              @select="handleOpeCdSelect"
              @clear="handlerOpeCdClear"
              :trigger-on-focus="true"
              style="width: 200px;"
              clearable
            >
              <template slot-scope="{ item }">
                <div class="autocomplete-item">
                  <div class="name">{{ item.value +'-'+ item.item.opeNm }}</div>
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>
          <span v-if="search.opeNm" class="tip-text">{{ search.opeNm }}</span>
        </el-col>
        
        <el-col :span="12" style="display: flex;align-items: center;">
          <el-form-item label="委托单位代码">
            <el-autocomplete
              v-model="search.merchId"
              :fetch-suggestions="queryMerchId"
              placeholder="请输入委托单位代码"
              @select="handleMerchIdSelect"
              @clear="handlerMerchIdClear"
              :trigger-on-focus="true"
              style="width: 200px;"
              clearable
            >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.item.prdtNm}}</div>
              </div>
            </template>
            </el-autocomplete>
          </el-form-item>
          <span v-if="search.merchNm" class="tip-text">{{ search.merchNm }}</span>
        </el-col>
      </el-row>
      <el-row :gutter="24" style="margin-top: 0;">
        <el-col :span="10">
          <el-form-item label="起始日期">
            <el-date-picker style="width:200px" v-model="search.startTime" type="date" value-format="yyyyMMdd" placeholder="请选择交易日期" clearable @change="validateEndTime"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止日期">
            <el-date-picker style="width: 200px;" v-model="search.endTime" type="date" value-format="yyyyMMdd" placeholder="请选择计划交易日期" clearable @change="validateEndTime"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <el-form-item label="状态">
            <el-select v-model="search.procFg" clearable placeholder="请选择交易状态" style="width: 200px;">
              <el-option
                v-for="dict in dict.type.xzp_ans_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>  
        <el-col :span="24" class="search-btns">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    </el-card>
    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      :before-open="beforeOpen"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      @row-update="rowUpdate"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <!-- 自定义操作按钮 -->
       <template slot-scope="{row,size,type}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-edit"
          @click.stop="handleUpdate(row)"
          v-hasPermi="['admin:tran:edit']"
        >编辑</el-button>
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-setting"
          @click.stop="handleAdjust(row)"
          v-hasPermi="['admin:tran:txn']"
        >
        调整
        </el-button>
        <el-button
          :size="size"
          :type="type"
          @click="handleCommand(row)"
          icon="el-icon-info"
        >查看</el-button>
      </template>
      <!-- 自定义状态列 -->
      <!-- <template slot="ansCd" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="getTagType(scope.row.ansCd)">
          {{ ansCd(scope.row) }}
        </el-tag>
      </template> -->
    </yo-table>
    
    
  </div>
</template>

<script>
import { batchTranCtrlList, updateBatchTranCtrl, batchUpdateTxnStaToZero } from '@/api/batchtranctrl';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import batchTranTableOption from './infoData/batchTranTableOption.js';

export default {
  name: 'batchtranctrl',
  dicts: ['xzp_ans_state'],
  data() {
    return {
      loading: false,
      formParent: {},
      search: {
        startTime: '',
        endTime: '',
        opeCd: '',
        opeNm:'',
        merchId: '',
        tranStatus: '',
        batchId: ''
      },
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      data: [],
      option: batchTranTableOption,
      multiple: true,
      // 自动完成相关数据
      opeCdOptions: [], // 业务代码选项
      merchIdOptions: [], // 委托单位代码选项
      opeCdLoading: false,
      merchIdLoading: false,
    };
  },
  created() {
    this.page.currentPage = 1;
    this.getList();
    // 更新字典数据
    this.updateDictData(this.option.column, 'procFg', this.dict.type.xzp_ans_state);
  },
  methods: {
    // 更新字典数据
    updateDictData(option, key, data) {
      // 更新表格配置中的状态选项
      const column = this.findObject(option, key);
      if (column) {
        column.dicData = data;
      }
    },
    handleCommand(row) {
      this.$refs.crud.rowView(row);
    },
    // 查找对象
    findObject(array, prop) {
      return array.find(item => item.prop === prop);
    },
    onLoad() {
      this.getList();
    },
    beforeOpen(done, type) {
      done();
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    async getList(query) {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      try {
        const { code, data } = await batchTranCtrlList(params);
        if (code === '0') {
          this.data = data.list;
          this.page.total = data.total;
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.page.currentPage = 1;
      this.getList();
    },
    resetQuery() {
      this.search = {
        startTime: '',
        endTime: '',
        opeCd: '',
        prdtNm: '',
        merchId: '',
        merchNm: '',
        tranStatus: '',
        batchId: ''
      };
      this.opeCdOptions = [];
      this.merchIdOptions = [];
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.multiple = !(selection.length > 0);
    },
    // 获取状态标签类型
    getTagType(ansCd) {
      const typeMap = {
        '1': 'success',
        '2': 'warning',
        '3': 'danger'
      };
      return typeMap[ansCd] || '';
    },
    // // 处理编辑
    handleUpdate(row) {
      this.formParent = JSON.parse(JSON.stringify(row));
      this.$refs.crud.rowEdit({
        ...row
      });
    },
    
    // 更新数据
    async rowUpdate(row, index, done, loading) {
      try {
        const { code } = await updateBatchTranCtrl(row);
        if (code === '0') {
          this.$message.success('更新成功');
          this.getList();
        } else {
          this.$message.error('更新失败');
        }
      } catch (error) {
        console.error('Error updating data:', error);
        this.$message.error('更新失败');
      } finally {
        done();
        loading();
      }
    },
    
    // 自定义状态列模板
    ansCd(row) {
      const dict = this.dict.type.xzp_ans_state;
      const item = dict.find(d => d.value === row.ansCd);
      return item ? item.label : row.ansCd;
    },
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opeCd
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 选择业务代码
    handleOpeCdSelect(item) {
      this.search.opeCd = item.value;
      this.search.opeNm = item.label;
      // 清空委托单位代码，触发重新查询
      if (this.search.merchId) {
        this.search.merchId = '';
        this.search.merchNm = '';
      }
    },

    handleDialogOpeCdSelect(item){
      this.editForm.opeCd = item.value;
      this.editForm.opeNm = item.item.opeNm;
      // 清空委托单位代码，触发重新查询
      if (this.editForm.merchId) {
        this.editForm.merchId = '';
        this.editForm.merchNm = '';
      }
    },
    // 清楚方法
    handlerOpeCdClear(){
      this.search.opeCd = ''
      this.search.opeNm = ''
    },
    handlerMerchIdClear(){
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    // 选择委托单位代码
    handleMerchIdSelect(item) {
      this.search.merchId = item.value;
      this.search.merchNm = item.item.prdtNm;
      // 如果没有业务代码，可以根据选择的委托单位代码查询相关的业务代码
      if (!this.search.opeCd) {
        // 可以在这里添加查询逻辑

      }
    },

    // 选择委托单位代码
    handleDialogMerchIdSelect(item) {
      this.editForm.merchId = item.value;
      this.editForm.merchNm = item.item.prdtNm;
      // 如果没有业务代码，可以根据选择的委托单位代码查询相关的业务代码
      if (!this.editForm.opeCd) {
        // 可以在这里添加查询逻辑

      }
    },
    async handleAdjust(row) {
      this.$confirm(`是否批量调整此外联批次号欠费库状态【${row.batchId}】？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const params = {
            opeCd: row.opeCd,
            merchId: row.merchId,
            wlBatchId: row.batchId
          };
          const res = await batchUpdateTxnStaToZero(params);
          const data = res.data;
          if (data > 0) {
            this.$message.success(`成功调整${data}条记录，状态已调整为：0-欠费`);
            this.getList();
          } else {
            this.$message.warning('欠费库无此批次记录，请核实。');
          }
        } catch (error) {
          this.$message.error('接口调用失败，请稍后重试。');
        }
      }).catch(() => {
        // 用户取消
      });
    },
    validateEndTime() {
      if (this.search.startTime && this.search.endTime) {
        if (Number(this.search.endTime) < Number(this.search.startTime)) {
          this.$message.warning('终止日期不能小于起始日期');
          this.search.endTime = '';
        }
      }
    },
  },
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}
.search-btns {
  text-align: right;
  padding-right: 50px;
}
.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 