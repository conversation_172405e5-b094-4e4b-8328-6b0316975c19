{"name": "define-property", "description": "Define a non-enumerable property on an object.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/define-property", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/define-property", "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-descriptor": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.2.0"}, "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "verb": {"related": {"list": ["extend-shallow", "merge-deep", "assign-deep", "mixin-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}