package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.SupAcctPayInstApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.SupAcctPayInst;
import com.xm.xzp.model.vo.SupAcctPayInstVo;
import com.xm.xzp.service.ISupAcctPayInstService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@Component
public class SupAcctPayInstController implements SupAcctPayInstApi {
    @Resource
    private ISupAcctPayInstService supAcctPayInstService;

    @Override
    @PMCTLLog(name = "查询监管账户支付指令列表", action = "查询")
    public RestResponse<PageInfo<SupAcctPayInst>> supAcctPayInstList(SupAcctPayInstVo supAcctPayInstVo, Integer pageNum, Integer pageSize) {
        PageInfo<SupAcctPayInst> pageList = supAcctPayInstService.supAcctPayInstList(
                supAcctPayInstVo, pageNum, pageSize);
        return RestResponse.success(pageList);
    }
}
