package com.xm.xzp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.mapper.TbZjjgAcctInfoMapper;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import com.xm.xzp.util.AdminConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 资金监管账户信息服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class TbZjjgAcctInfoServiceImpl extends ServiceImpl<TbZjjgAcctInfoMapper, TbZjjgAcctInfo> implements ITbZjjgAcctInfoService {

    @Resource
    private TbZjjgAcctInfoMapper tbZjjgAcctInfoMapper;

    @Override
    public List<TbZjjgAcctInfo> suggestAcctInfo(String keyword) {
        try {
            String trimmedKeyword = StringUtils.hasText(keyword) ? keyword.trim() : null;
            List<TbZjjgAcctInfo> result = tbZjjgAcctInfoMapper.selectAcctInfoByKeyword(trimmedKeyword);
            if (StringUtils.hasText(keyword)) {
                log.info("根据关键字[{}]查询到{}条账户信息", keyword, result.size());
            } else {
                log.info("查询全量账户信息，共{}条", result.size());
            }
            return result;
        } catch (Exception e) {
            log.error("查询账户信息失败，关键字：{}", keyword, e);
            return Collections.emptyList();
        }
    }

    @Override
    public PageInfo<TbZjjgAcctInfo> queryAcctInfoPage(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize) {
        try {
            log.info("开始分页查询账户信息，查询条件：{}，页码：{}，每页数量：{}", queryVo, pageNum, pageSize);
            log.info("状态查询参数 othMsg1Tx：{}，参数类型：{}", queryVo.getOthMsg1Tx(),
                    queryVo.getOthMsg1Tx() != null ? queryVo.getOthMsg1Tx().getClass().getSimpleName() : "null");

            // 详细的条件判断日志
            if (queryVo.getOthMsg1Tx() != null && !queryVo.getOthMsg1Tx().isEmpty()) {
                if ("1".equals(queryVo.getOthMsg1Tx())) {
                    log.info("查询条件：查询已删除记录（oth_msg1_tx = '1'）");
                } else if ("0".equals(queryVo.getOthMsg1Tx())) {
                    log.info("查询条件：查询正常记录（oth_msg1_tx IS NULL OR oth_msg1_tx != '1'）");
                } else {
                    log.info("查询条件：未知状态值 [{}]，将使用默认条件", queryVo.getOthMsg1Tx());
                }
            } else {
                log.info("查询条件：默认查询正常记录（oth_msg1_tx IS NULL OR oth_msg1_tx != '1'）");
            }

            PageHelper.startPage(pageNum, pageSize);
            List<TbZjjgAcctInfo> list = tbZjjgAcctInfoMapper.selectAcctInfoByCondition(queryVo);

            log.info("分页查询账户信息完成，查询到{}条记录", list.size());

            // 默认对敏感信息进行脱敏处理
            for (TbZjjgAcctInfo info : list) {
                // 设置默认脱敏状态
                info.setSensitiveStatus("1"); // 1-脱敏

                // 对监管账户进行脱敏
                String cpabAccId = info.getCpabAccId();
                if (StringUtils.hasText(cpabAccId)) {
                    cpabAccId = cpabAccId.replaceAll("(?<=.{3}).(?=.{4})", "*");
                    info.setCpabAccId(cpabAccId);
                }

                // 对账户名进行脱敏
                String acctNm = info.getAcctNm();
                if (StringUtils.hasText(acctNm)) {
                    acctNm = acctNm.replaceAll("(?<=.{1}).", "*");
                    info.setAcctNm(acctNm);
                }
            }

            // 打印前几条记录的状态信息用于调试
            if (!list.isEmpty()) {
                for (int i = 0; i < Math.min(3, list.size()); i++) {
                    TbZjjgAcctInfo info = list.get(i);
                    log.debug("记录{}：账户={}，户名={}，状态={}", i+1, info.getCpabAccId(), info.getAcctNm(), info.getOthMsg1Tx());
                }
            }

            return new PageInfo<>(list);
        } catch (Exception e) {
            log.error("分页查询账户信息失败，查询条件：{}", queryVo, e);
            return new PageInfo<>(Collections.emptyList());
        }
    }

    @Override
    public boolean addAcctInfo(TbZjjgAcctInfo acctInfo) {
        try {
            // 检查是否存在重复的账户信息（根据公司账户和户名判断）
            if (StringUtils.hasText(acctInfo.getCpabAccId()) && StringUtils.hasText(acctInfo.getAcctNm())) {
                long count = this.lambdaQuery()
                        .eq(TbZjjgAcctInfo::getCpabAccId, acctInfo.getCpabAccId())
                        .eq(TbZjjgAcctInfo::getAcctNm, acctInfo.getAcctNm())
                        .count();
                if (count > 0) {
                    log.warn("账户信息已存在，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
                    return false;
                }
            }

            // 设置业务类型为资金监管
            if (!StringUtils.hasText(acctInfo.getTransType())) {
                acctInfo.setTransType("zzg");
            }

            // 设置交易日期（如果未设置）
            if (!StringUtils.hasText(acctInfo.getTranDt())) {
                acctInfo.setTranDt(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")));
            }

            // 设置交易时间戳（如果未设置）
            if (!StringUtils.hasText(acctInfo.getTranTime())) {
                acctInfo.setTranTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            // 为新增记录生成唯一标识（如果未设置）
            if (!StringUtils.hasText(acctInfo.getOthMsg2Tx())) {
                acctInfo.setOthMsg2Tx(java.util.UUID.randomUUID().toString());
                log.info("为新增记录生成唯一标识：{}", acctInfo.getOthMsg2Tx());
            }

            boolean result = this.save(acctInfo);
            if (result) {
                log.info("新增账户信息成功，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            } else {
                log.warn("新增账户信息失败，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            }
            return result;
        } catch (Exception e) {
            log.error("新增账户信息异常，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm(), e);
            return false;
        }
    }

    @Override
    public boolean updateAcctInfo(TbZjjgAcctInfo acctInfo) {
        try {
            // 先查询记录是否存在（不考虑删除状态）
            TbZjjgAcctInfo existingInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getTranTime, acctInfo.getTranTime())
                    .eq(TbZjjgAcctInfo::getOpeCd, acctInfo.getOpeCd())
                    .eq(TbZjjgAcctInfo::getMerchId, acctInfo.getMerchId())
                    .one();

            if (existingInfo == null) {
                log.warn("修改账户信息失败，记录不存在，交易时间：{}，业务代码：{}，委托单位：{}",
                        acctInfo.getTranTime(), acctInfo.getOpeCd(), acctInfo.getMerchId());
                return false;
            }

            // 检查记录是否已被删除
            if ("1".equals(existingInfo.getOthMsg1Tx())) {
                log.warn("修改账户信息失败，记录已被删除，交易时间：{}，业务代码：{}，委托单位：{}，状态：{}",
                        acctInfo.getTranTime(), acctInfo.getOpeCd(), acctInfo.getMerchId(), existingInfo.getOthMsg1Tx());
                return false;
            }

            // 检查并防止敏感字段被脱敏后的值覆盖
            String originalCpabAccId = existingInfo.getCpabAccId();
            String originalAcctNm = existingInfo.getAcctNm();

            // 检查是否为脱敏数据（包含*号）
            if (StringUtils.hasText(acctInfo.getCpabAccId()) && acctInfo.getCpabAccId().contains("*")) {
                log.warn("检测到脱敏后的公司账户数据，保持原值不变。原值：{}，脱敏值：{}",
                        originalCpabAccId, acctInfo.getCpabAccId());
                acctInfo.setCpabAccId(originalCpabAccId);
            }

            if (StringUtils.hasText(acctInfo.getAcctNm()) && acctInfo.getAcctNm().contains("*")) {
                log.warn("检测到脱敏后的账户名数据，保持原值不变。原值：{}，脱敏值：{}",
                        originalAcctNm, acctInfo.getAcctNm());
                acctInfo.setAcctNm(originalAcctNm);
            }

            // 验证必填字段（使用处理后的值）
            if (!StringUtils.hasText(acctInfo.getCpabAccId()) || !StringUtils.hasText(acctInfo.getAcctNm())) {
                log.error("修改账户信息失败，公司账户和户名不能为空");
                return false;
            }

            log.info("准备修改账户信息，公司账户：{}，户名：{}，当前状态：{}",
                    acctInfo.getCpabAccId(), acctInfo.getAcctNm(), existingInfo.getOthMsg1Tx());

            // 更新交易时间戳
            if (!StringUtils.hasText(acctInfo.getTranTime())) {
                acctInfo.setTranTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            // 执行更新
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getCpabAccId, originalCpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, originalAcctNm)
                    .update(acctInfo);

            if (result) {
                log.info("修改账户信息成功，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            } else {
                log.warn("修改账户信息失败，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            }
            return result;
        } catch (Exception e) {
            log.error("修改账户信息异常，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm(), e);
            return false;
        }
    }

    @Override
    public boolean deleteAcctInfo(String cpabAccId, String acctNm) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(cpabAccId) || !StringUtils.hasText(acctNm)) {
                log.error("删除账户信息失败，公司账户和户名不能为空");
                return false;
            }

            // 先查询记录是否存在（不考虑删除状态）
            TbZjjgAcctInfo existingInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .one();

            if (existingInfo == null) {
                log.warn("删除账户信息失败，记录不存在，公司账户：{}，户名：{}", cpabAccId, acctNm);
                return false;
            }

            // 检查记录是否已被删除
            if ("1".equals(existingInfo.getOthMsg1Tx())) {
                log.warn("删除账户信息失败，记录已被删除，公司账户：{}，户名：{}，状态：{}",
                        cpabAccId, acctNm, existingInfo.getOthMsg1Tx());
                return false;
            }

            log.info("准备删除账户信息，公司账户：{}，户名：{}，当前状态：{}",
                    cpabAccId, acctNm, existingInfo.getOthMsg1Tx());

            // 执行逻辑删除：设置oth_msg1_tx为"1"表示删除状态
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .update();

            if (result) {
                log.info("逻辑删除账户信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            } else {
                log.warn("逻辑删除账户信息失败，公司账户：{}，户名：{}", cpabAccId, acctNm);
            }
            return result;
        } catch (Exception e) {
            log.error("删除账户信息异常，公司账户：{}，户名：{}", cpabAccId, acctNm, e);
            return false;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctInfo(String cpabAccId, String acctNm) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(cpabAccId) || !StringUtils.hasText(acctNm)) {
                log.error("查询账户信息失败，公司账户和户名不能为空");
                return null;
            }

            TbZjjgAcctInfo result = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .one();

            if (result != null) {
                log.debug("查询账户信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            } else {
                log.debug("查询账户信息为空，公司账户：{}，户名：{}", cpabAccId, acctNm);
            }
            return result;
        } catch (Exception e) {
            log.error("查询账户信息异常，公司账户：{}，户名：{}", cpabAccId, acctNm, e);
            return null;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus) {
        try {
            log.info("获取账户敏感信息，公司账户：{}，户名：{}，敏感状态：{}", cpabAccId, acctNm, sensitiveStatus);

            // 检查必填字段
            if (!StringUtils.hasText(cpabAccId) || !StringUtils.hasText(acctNm)) {
                log.error("获取账户敏感信息失败，公司账户和户名不能为空");
                return null;
            }

            // 查询原始数据
            TbZjjgAcctInfo originalInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .one();

            if (originalInfo == null) {
                log.warn("获取账户敏感信息失败，记录不存在，公司账户：{}，户名：{}", cpabAccId, acctNm);
                return null;
            }

            // 组装返回信息
            TbZjjgAcctInfo sensitiveInfoVO = new TbZjjgAcctInfo();
            BeanUtils.copyProperties(originalInfo, sensitiveInfoVO);

            // 根据敏感状态进行处理
            if (SensitiveStatusEnum.SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
                // 需要脱敏
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

                // 对监管账户进行脱敏
                String cpabAccIdValue = sensitiveInfoVO.getCpabAccId();
                if (StringUtils.hasText(cpabAccIdValue)) {
                    cpabAccIdValue = cpabAccIdValue.replaceAll("(?<=.{3}).(?=.{4})", "*");
                    sensitiveInfoVO.setCpabAccId(cpabAccIdValue);
                }

                // 对账户名进行脱敏
                String acctNmValue = sensitiveInfoVO.getAcctNm();
                if (StringUtils.hasText(acctNmValue)) {
                    acctNmValue = acctNmValue.replaceAll("(?<=.{1}).", "*");
                    sensitiveInfoVO.setAcctNm(acctNmValue);
                }
            } else {
                // 不脱敏，返回原始数据
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
            }

            log.info("获取账户敏感信息成功，公司账户：{}，户名：{}，敏感状态：{}",
                    cpabAccId, acctNm, sensitiveInfoVO.getSensitiveStatus());
            return sensitiveInfoVO;
        } catch (Exception e) {
            log.error("获取账户敏感信息异常，公司账户：{}，户名：{}", cpabAccId, acctNm, e);
            return null;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctSensitiveInfoByBizKey(String tranTime, String opeCd, String merchId, String sensitiveStatus) {
        try {
            log.info("根据业务标识获取账户敏感信息，交易时间：{}，业务代码：{}，委托单位：{}，敏感状态：{}",
                    tranTime, opeCd, merchId, sensitiveStatus);

            // 检查必填字段
            if (!StringUtils.hasText(tranTime) || !StringUtils.hasText(opeCd) || !StringUtils.hasText(merchId)) {
                log.error("获取账户敏感信息失败，交易时间、业务代码和委托单位代码不能为空");
                return null;
            }

            // 查询原始数据
            TbZjjgAcctInfo originalInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getTranTime, tranTime)
                    .eq(TbZjjgAcctInfo::getOpeCd, opeCd)
                    .eq(TbZjjgAcctInfo::getMerchId, merchId)
                    .one();

            if (originalInfo == null) {
                log.warn("获取账户敏感信息失败，记录不存在，交易时间：{}，业务代码：{}，委托单位：{}",
                        tranTime, opeCd, merchId);
                return null;
            }

            // 组装返回信息
            TbZjjgAcctInfo sensitiveInfoVO = new TbZjjgAcctInfo();
            BeanUtils.copyProperties(originalInfo, sensitiveInfoVO);

            // 根据敏感状态进行处理
            log.info("处理敏感状态，请求状态：{}，脱敏状态码：{}，不脱敏状态码：{}",
                    sensitiveStatus, SensitiveStatusEnum.SENSITIVE_STATUS.statusCode(),
                    SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());

            if (SensitiveStatusEnum.SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
                // 需要脱敏
                log.info("执行脱敏处理");
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

                // 对监管账户进行脱敏
                String cpabAccIdValue = sensitiveInfoVO.getCpabAccId();
                if (StringUtils.hasText(cpabAccIdValue)) {
                    String originalValue = cpabAccIdValue;
                    cpabAccIdValue = cpabAccIdValue.replaceAll("(?<=.{3}).(?=.{4})", "*");
                    sensitiveInfoVO.setCpabAccId(cpabAccIdValue);
                    log.info("账户脱敏：{} -> {}", originalValue, cpabAccIdValue);
                }

                // 对账户名进行脱敏
                String acctNmValue = sensitiveInfoVO.getAcctNm();
                if (StringUtils.hasText(acctNmValue)) {
                    String originalValue = acctNmValue;
                    acctNmValue = acctNmValue.replaceAll("(?<=.{1}).", "*");
                    sensitiveInfoVO.setAcctNm(acctNmValue);
                    log.info("账户名脱敏：{} -> {}", originalValue, acctNmValue);
                }
            } else {
                // 不脱敏，返回原始数据
                log.info("返回原始数据，不进行脱敏");
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
                log.info("原始数据：账户={}，账户名={}", sensitiveInfoVO.getCpabAccId(), sensitiveInfoVO.getAcctNm());
            }

            log.info("根据业务标识获取账户敏感信息成功，交易时间：{}，业务代码：{}，委托单位：{}，敏感状态：{}",
                    tranTime, opeCd, merchId, sensitiveInfoVO.getSensitiveStatus());
            return sensitiveInfoVO;
        } catch (Exception e) {
            log.error("根据业务标识获取账户敏感信息异常，交易时间：{}，业务代码：{}，委托单位：{}",
                    tranTime, opeCd, merchId, e);
            return null;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctSensitiveInfoByOthMsg2Tx(String othMsg2Tx, String sensitiveStatus) {
        try {
            log.info("根据唯一标识获取账户敏感信息，唯一标识：{}，敏感状态：{}", othMsg2Tx, sensitiveStatus);

            // 检查必填字段
            if (!StringUtils.hasText(othMsg2Tx)) {
                log.error("根据唯一标识获取账户敏感信息失败，唯一标识不能为空");
                return null;
            }

            // 查询原始数据
            TbZjjgAcctInfo originalInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
                    .one();

            if (originalInfo == null) {
                log.warn("根据唯一标识获取账户敏感信息失败，记录不存在，唯一标识：{}", othMsg2Tx);
                return null;
            }

            // 组装返回信息
            TbZjjgAcctInfo sensitiveInfoVO = new TbZjjgAcctInfo();
            BeanUtils.copyProperties(originalInfo, sensitiveInfoVO);

            // 根据敏感状态处理数据
            if ("1".equals(sensitiveStatus)) {
                // 脱敏处理
                log.info("对敏感信息进行脱敏处理");
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

                // 对公司账户进行脱敏
                String cpabAccIdValue = sensitiveInfoVO.getCpabAccId();
                if (StringUtils.hasText(cpabAccIdValue)) {
                    String originalValue = cpabAccIdValue;
                    cpabAccIdValue = cpabAccIdValue.replaceAll("(?<=.{3}).(?=.{4})", "*");
                    sensitiveInfoVO.setCpabAccId(cpabAccIdValue);
                    log.info("账户脱敏：{} -> {}", originalValue, cpabAccIdValue);
                }

                // 对账户名进行脱敏
                String acctNmValue = sensitiveInfoVO.getAcctNm();
                if (StringUtils.hasText(acctNmValue)) {
                    String originalValue = acctNmValue;
                    acctNmValue = acctNmValue.replaceAll("(?<=.{1}).", "*");
                    sensitiveInfoVO.setAcctNm(acctNmValue);
                    log.info("账户名脱敏：{} -> {}", originalValue, acctNmValue);
                }
            } else {
                // 不脱敏，返回原始数据
                log.info("返回原始数据，不进行脱敏");
                sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
                log.info("原始数据：账户={}，账户名={}", sensitiveInfoVO.getCpabAccId(), sensitiveInfoVO.getAcctNm());
            }

            log.info("根据唯一标识获取账户敏感信息成功，唯一标识：{}，敏感状态：{}",
                    othMsg2Tx, sensitiveInfoVO.getSensitiveStatus());
            return sensitiveInfoVO;
        } catch (Exception e) {
            log.error("根据唯一标识获取账户敏感信息异常，唯一标识：{}", othMsg2Tx, e);
            return null;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctInfoByOthMsg2Tx(String othMsg2Tx) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(othMsg2Tx)) {
                log.error("根据唯一标识查询账户信息失败，唯一标识不能为空");
                return null;
            }

            TbZjjgAcctInfo result = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
                    .one();

            if (result != null) {
                log.debug("根据唯一标识查询账户信息成功，唯一标识：{}", othMsg2Tx);
            } else {
                log.debug("根据唯一标识查询账户信息为空，唯一标识：{}", othMsg2Tx);
            }
            return result;
        } catch (Exception e) {
            log.error("根据唯一标识查询账户信息异常，唯一标识：{}", othMsg2Tx, e);
            return null;
        }
    }

    @Override
    public boolean updateAcctInfoByOthMsg2Tx(TbZjjgAcctInfo acctInfo) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(acctInfo.getOthMsg2Tx())) {
                log.error("根据唯一标识修改账户信息失败，唯一标识不能为空");
                return false;
            }

            // 先查询记录是否存在（不考虑删除状态）
            TbZjjgAcctInfo existingInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, acctInfo.getOthMsg2Tx())
                    .one();

            if (existingInfo == null) {
                log.warn("根据唯一标识修改账户信息失败，记录不存在，唯一标识：{}", acctInfo.getOthMsg2Tx());
                return false;
            }

            // 检查记录是否已被删除
            if ("1".equals(existingInfo.getOthMsg1Tx())) {
                log.warn("根据唯一标识修改账户信息失败，记录已被删除，唯一标识：{}，状态：{}",
                        acctInfo.getOthMsg2Tx(), existingInfo.getOthMsg1Tx());
                return false;
            }

            log.info("准备根据唯一标识修改账户信息，唯一标识：{}，当前状态：{}",
                    acctInfo.getOthMsg2Tx(), existingInfo.getOthMsg1Tx());

            // 检查并防止敏感字段被脱敏后的值覆盖
            String originalCpabAccId = existingInfo.getCpabAccId();
            String originalAcctNm = existingInfo.getAcctNm();

            // 检查是否为脱敏数据（包含*号）
            if (StringUtils.hasText(acctInfo.getCpabAccId()) && acctInfo.getCpabAccId().contains("*")) {
                log.warn("检测到脱敏后的公司账户数据，保持原值不变。原值：{}，脱敏值：{}",
                        originalCpabAccId, acctInfo.getCpabAccId());
                acctInfo.setCpabAccId(originalCpabAccId);
            }

            if (StringUtils.hasText(acctInfo.getAcctNm()) && acctInfo.getAcctNm().contains("*")) {
                log.warn("检测到脱敏后的账户名数据，保持原值不变。原值：{}，脱敏值：{}",
                        originalAcctNm, acctInfo.getAcctNm());
                acctInfo.setAcctNm(originalAcctNm);
            }

            // 验证必填字段（使用处理后的值）
            if (!StringUtils.hasText(acctInfo.getCpabAccId()) || !StringUtils.hasText(acctInfo.getAcctNm())) {
                log.error("根据唯一标识修改账户信息失败，公司账户和户名不能为空");
                return false;
            }

            // 更新交易时间戳
            if (!StringUtils.hasText(acctInfo.getTranTime())) {
                acctInfo.setTranTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            // 执行更新
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, acctInfo.getOthMsg2Tx())
                    .update(acctInfo);

            if (result) {
                log.info("根据唯一标识修改账户信息成功，唯一标识：{}，公司账户：{}，账户名：{}",
                        acctInfo.getOthMsg2Tx(), acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            } else {
                log.warn("根据唯一标识修改账户信息失败，唯一标识：{}", acctInfo.getOthMsg2Tx());
            }
            return result;
        } catch (Exception e) {
            log.error("根据唯一标识修改账户信息异常，唯一标识：{}", acctInfo.getOthMsg2Tx(), e);
            return false;
        }
    }

    @Override
    public boolean deleteAcctInfoByOthMsg2Tx(String othMsg2Tx) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(othMsg2Tx)) {
                log.error("根据唯一标识删除账户信息失败，唯一标识不能为空");
                return false;
            }

            // 先查询记录是否存在
            TbZjjgAcctInfo existingInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
                    .one();

            if (existingInfo == null) {
                log.warn("根据唯一标识删除账户信息失败，记录不存在，唯一标识：{}", othMsg2Tx);
                return false;
            }

            // 检查记录是否已被删除
            if ("1".equals(existingInfo.getOthMsg1Tx())) {
                log.warn("根据唯一标识删除账户信息失败，记录已被删除，唯一标识：{}，状态：{}",
                        othMsg2Tx, existingInfo.getOthMsg1Tx());
                return false;
            }

            log.info("准备根据唯一标识删除账户信息，唯一标识：{}，当前状态：{}",
                    othMsg2Tx, existingInfo.getOthMsg1Tx());

            // 执行逻辑删除（设置状态为1）
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getOthMsg2Tx, othMsg2Tx)
                    .set(TbZjjgAcctInfo::getOthMsg1Tx, "1")
                    .update();

            if (result) {
                log.info("根据唯一标识删除账户信息成功，唯一标识：{}", othMsg2Tx);
            } else {
                log.warn("根据唯一标识删除账户信息失败，唯一标识：{}", othMsg2Tx);
            }
            return result;
        } catch (Exception e) {
            log.error("根据唯一标识删除账户信息异常，唯一标识：{}", othMsg2Tx, e);
            return false;
        }
    }
}