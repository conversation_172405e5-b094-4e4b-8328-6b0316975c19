# 逻辑删除问题排查和修复说明

## 问题现象

1. **状态查询条件没有生效**：前端传入`othMsg1Tx: "1"`，但查询结果不正确
2. **删除和修改失败**：提示"删除账户信息失败，可能是记录不存在"

## 问题分析

### 1. SQL查询逻辑问题

**原始SQL逻辑**：
```xml
<choose>
    <when test="queryVo.othMsg1Tx != null and queryVo.othMsg1Tx != ''">
        <if test="queryVo.othMsg1Tx == '1'">
            oth_msg1_tx = '1'
        </if>
        <if test="queryVo.othMsg1Tx == '0'">
            (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
        </if>
    </when>
    <otherwise>
        (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    </otherwise>
</choose>
```

**问题**：使用了两个独立的`<if>`标签，当`othMsg1Tx = '1'`时，两个条件都不会执行，导致没有任何WHERE条件。

### 2. MyBatis-Plus查询条件问题

**原始代码**：
```java
.ne(TbZjjgAcctInfo::getOthMsg1Tx, "1") // 排除已删除的记录
```

**问题**：`ne()`方法不会匹配NULL值，导致NULL值的记录被排除。

## 修复方案

### 1. 修复SQL查询逻辑

**修复后的SQL**：
```xml
<choose>
    <when test="queryVo.othMsg1Tx != null and queryVo.othMsg1Tx != ''">
        <choose>
            <when test="queryVo.othMsg1Tx == '1'">
                oth_msg1_tx = '1'
            </when>
            <when test="queryVo.othMsg1Tx == '0'">
                (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
            </when>
        </choose>
    </when>
    <otherwise>
        (oth_msg1_tx IS NULL OR oth_msg1_tx != '1')
    </otherwise>
</choose>
```

**改进**：使用嵌套的`<choose>`标签，确保条件互斥且完整。

### 2. 修复MyBatis-Plus查询条件

**修复后的代码**：
```java
.and(wrapper -> wrapper.isNull(TbZjjgAcctInfo::getOthMsg1Tx)
        .or().ne(TbZjjgAcctInfo::getOthMsg1Tx, "1")) // 排除已删除的记录
```

**改进**：使用`and()`方法组合条件，同时处理NULL值和非'1'值。

## 修复的文件

### 1. Mapper XML文件
**文件**: `xm-xzp-impl/src/main/resources/mapper/TbZjjgAcctInfoMapper.xml`
- 修复了分页查询的状态条件逻辑

### 2. Service实现类
**文件**: `xm-xzp-impl/src/main/java/com/xm/xzp/service/impl/TbZjjgAcctInfoServiceImpl.java`
- 修复了删除方法中的记录存在性检查
- 修复了修改方法中的记录存在性检查

## 测试验证

### 1. 数据库测试
执行`测试状态查询SQL.sql`中的SQL语句，验证：
- 正常状态记录的查询
- 已删除状态记录的查询
- 数据的状态分布情况

### 2. 功能测试
1. **状态查询测试**：
   - 选择"正常"状态，应该显示未删除的记录
   - 选择"已删除"状态，应该显示已删除的记录
   - 不选择状态，应该默认显示未删除的记录

2. **删除功能测试**：
   - 删除正常状态的记录，应该成功
   - 删除已删除状态的记录，应该提示已删除
   - 删除后记录状态应该变为"已删除"

3. **修改功能测试**：
   - 修改正常状态的记录，应该成功
   - 修改已删除状态的记录，应该失败

## 数据状态说明

### 状态值定义
- `NULL` 或 `!= '1'`：正常状态
- `'1'`：已删除状态

### 状态转换
- 新增记录：`oth_msg1_tx = NULL`（正常状态）
- 逻辑删除：`oth_msg1_tx = '1'`（已删除状态）
- 恢复记录：`oth_msg1_tx = NULL`（正常状态）

## 注意事项

1. **NULL值处理**：在SQL和Java代码中都要正确处理NULL值
2. **状态一致性**：确保前端显示的状态与数据库中的状态一致
3. **权限控制**：已删除的记录不应该被修改，但可以被查看
4. **日志记录**：所有状态变更都应该有详细的日志记录

## 预期结果

修复后应该实现：
1. 状态查询功能正常工作
2. 删除功能正常工作（逻辑删除）
3. 修改功能正常工作（不能修改已删除记录）
4. 前端状态显示正确
5. 数据一致性得到保证
