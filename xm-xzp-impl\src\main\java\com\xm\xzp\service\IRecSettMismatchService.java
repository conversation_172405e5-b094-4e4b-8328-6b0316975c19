package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.RecSettMismatch;
import com.xm.xzp.model.vo.RecSettMismatchVo;
import org.apache.poi.ss.usermodel.Workbook;

public interface IRecSettMismatchService  extends IService<RecSettMismatch> {
    /**
     * 分页查询对账/清算不一致记录
     *
     * @param recSettMismatch 查询条件
     * @param pageNum       当前页
     * @param pageSize      每页数量
     * @return 分页结果
     */
    PageInfo<RecSettMismatch> recSettMismatchList(RecSettMismatchVo recSettMismatch, Integer pageNum, Integer pageSize);

    /**
     * 编辑对账/清算不一致信息
     *
     * @param recSettMismatch 对账/清算不一致信息
     * @return 是否成功
     */
    boolean editRecSettMismatch(RecSettMismatch recSettMismatch);

    /**
     * 导出对账/清算不一致信息
     *
     * @param recSettMismatchVo 查询条件
     * @return 导出结果
     */
    Workbook exportRecSettMismatch(RecSettMismatchVo recSettMismatchVo);
}
