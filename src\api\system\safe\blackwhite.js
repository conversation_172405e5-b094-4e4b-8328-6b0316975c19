import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/youi";
/** 
*2022/7/14
*@param {* ipStart:起始IP地址,ipEnd:截止IP地址,listType:名单类型,pageNum:当前页数,pageSize:每页几条} query
*/ 
export function list (data) {
    const { ipStart = "", ipEnd = "", listType = "",currentPage = 1,pageSize = 10 } = data || {}
    return request({
        url:`/api/security/black-white/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            ipStart,
            ipEnd,
            listType
        }
    })
}

//新增
export function add (data) {
    const { ipStart ="", ipEnd = "", listType = "",status = "" } = data || {}
    return request({
        url: '/api/security/black-white/actions/add',
        method: 'post',
        data: {
          ipStart,
          ipEnd,
          listType,
          status
        }
    })
    
}

//删除
export function del (id) {
    return request({
        url: '/api/security/black-white/actions/remove/'+ id,
        method: 'post',
    })
}


//批量删除
export function dels (ids) {
    return request({
        url: '/api/security/black-white/actions/remove',
        method: 'post',
        data: ids
    })
}


// 修改岗位
export function update (data) {
    return request({
      url: '/api/security/black-white/actions/edit',
      method: 'post',
      data: data
    })
}

// 修改状态
export function updateStatus(data) {
    return request({
      url: '/api/security/black-white/actions/on-off',
      method: 'post',
      data: data
    })
}
