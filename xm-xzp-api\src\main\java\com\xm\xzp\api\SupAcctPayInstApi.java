package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.SupAcctPayInst;
import com.xm.xzp.model.vo.SupAcctPayInstVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "监管账户支付指令查询")
@Validated
public interface SupAcctPayInstApi {

    @ApiOperation(value = "监管账户支付指令列表", notes = "supAcctPayInstList")
    @PostMapping("/supAcctPayInstList")
    RestResponse<PageInfo<SupAcctPayInst>> supAcctPayInstList(@RequestBody SupAcctPayInstVo supAcctPayInstVo,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);
}
