import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/youi";

/**
 * 2022/2/28
 * @param {*userName:用户编号,userNickname:用户姓名,pageNum：当前页数,pageSize：每页几条} query
 */
// 分页查询用户列表
export function listUser (query) {
  const { currentPage = 1, pageSize = 10, userName = '', userNickname = '',userStat = '',empStatus = '' } = query || {}
  return request({
    url: `/api/admin/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      userName,
      userNickname,
      userStat,
      empStatus,
    }
  })
}

//查询角色列表
export function listRole (data) {
  const { roleId = "", roleName = "", status = "1" } = data || {}
  return request({
    url: `/api/admin/users/roles/actions/list`,
    method: 'post',
    data: {
      roleId,
      roleName,
      status
    }
  })
}
/**
 * 2022/7/28
 * 查询渠道未注册用户信息
 * @param  {*chgTellerNo: 用户名}
 * @param {*tellerName: 姓名}
 */
export function userUnregisterInfo (data) {
  const { chgTellerNo = "", tellerName = "", currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/teller-sync/unregister/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: {
      chgTellerNo,
      tellerName
    }
  })
}
/**
 * 2022/7/28
 *  注册用户
 * @param  {*chgTellerNo: 用户名}
 * @param {*tellerName: 姓名}
 */
export function registerUser(data) {
  return request({
    url: `/api/admin/teller-sync/actions/register`,
    method: 'post',
    data
  })
}
/**
 * 2022/3/1
 * @param {*} userId
 */
// 查询用户详细
export function getUser (userId) {
  return request({
    url: '/api/admin/users/actions/get/' + parseStrEmpty(userId),
    method: 'get'
  })
}

//5.20.查询用户敏感信息
export function usersSensitive (userName,sensitiveStatus) {
  return request({
    url: `/api/admin/users/sensitive/actions/get/${userName}/${sensitiveStatus}`,
    method: 'get',
  })
}

// 新增用户

export function addUser (data) {
  const { deptIds = "", displayOrder = "", email = "", gender = "", idCard = "", instId = "",
 phoneNumber = "", roleIds = "", userName = "", userNickname = "", userStat = "", empStatus = "" } = data || {}
  return request({
    url: '/api/admin/users/actions/add',
    method: 'post',
    data: {
      deptIds,
      displayOrder,
      email,
      gender,
      idCard,
      instId,
      phoneNumber,
      roleIds,
      userName,
      userNickname,
      userStat,
      empStatus,
    }
  })
}

// 修改用户
export function updateUser (data) {
  const {userId = "", deptIds = "", displayOrder = "", email = null, gender = "", idCard = null, instId = "",
 phoneNumber = null, roleIds = "", userName = "", userNickname = "", userStat = "", empStatus = "" } = data || {}
  return request({
    url: '/api/admin/users/actions/edit',
    method: 'post',
    data: {
      userId,
      deptIds,
      displayOrder,
      email,
      gender,
      idCard,
      instId,
      phoneNumber,
      roleIds,
      userName,
      userNickname,
      userStat,
      empStatus,
    }
  })
}

// 删除用户
export function delUser (userName) {
  return request({
    url: '/api/admin/users/actions/remove/'+ userName,
    method: 'post',
  })
}

// 批量删除用户
export function delUsers (userNames) {
  return request({
    url: '/api/admin/users/actions/remove',
    method: 'post',
    data:userNames
  })
}

// 用户密码重置
export function resetUserPwd (data) {
  return request({
    url: '/api/admin/users/passwd/actions/reset',
    method: 'post',
    data: data
  })
}

//导出用户信息接口
export function exportUsers(data) {
  return request({
    url: '/api/admin/users/actions/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//下载用户信息导入模板
export function downloadExample(data) {
  return request({
    url: '/api/admin/users/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob'
  })
}

//导入用户信息接口
export function importUsers(data) {
  return request({
    url: '/api/admin/users/actions/import',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

// 查询用户个人信息
export function getUserProfile () {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile (data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd (oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar (data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole (userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole (data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}
