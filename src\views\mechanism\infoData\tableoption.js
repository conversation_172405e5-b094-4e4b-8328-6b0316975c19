import validate from "@/utils/validate"
export default {
  index: true,
  indexLabel: '序号',
  // selection: true,
  align: 'center',
  card: true,
  // searchSpan: 6,
  searchMenuSpan: 6,
  // searchMenuPosition: 'left',
  // searchLabelWidth: 10,
  addTitle: '新增机构',
  editTitle: '修改机构',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  emptyBtn: false,
  refreshBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  maxHeight: 450,
  tip: false,
  columnBtn: false,
  column: [{
    label: '机构编号',
    prop: 'instId',
    search: true,
    editDisabled: true,
    rules: [{
      required: true,
      message: "请输入机构编号",
      trigger: "blur"
    },
    { min: 1, max: 8, message: '长度在 1 到 8 个字符', trigger: 'blur' },
    { validator: validate.blankSpace, trigger: 'blur'},
    { validator: validate.roleIdValidate, trigger: 'blur'}
    ]
  }, {
    label: '机构名称',
    prop: 'instName',
    hide: true,
    showColumn: false,
    rules: [{
      required: true,
      message: "请输入机构名称",
      trigger: "blur"
    },
    { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
    {
      validator: validate.blankSpace, trigger: 'blur'
    }]
  },
  {
    label: '机构简称',
    prop: 'instNameAbbr',
    search: true,
    rules: [{
      required: true,
      message: "请输入机构简称",
      trigger: "blur"
    },
    { min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur' },
    {
      validator: validate.blankSpace, trigger: 'blur'
    }]
  }, {
    label: '机构英文名称',
    prop: 'instEname',
    hide: true,
    showColumn: false,
    rules: [
    { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
    ]
  },{
    label: '上级机构',
    prop: 'upInstId',
    formslot:true,
    editDisplay:false,
      viewDisplay:false,
    rules: [{
      required: true,
      message: "请选择上级机构",
      trigger: "change"
    },]
  },
  {
    label: '上级机构',
    prop: 'upInstId1',
    hide: true,
    editDisabled: true,
    addDisplay: false,
  },
  {
    label: '上级机构简称',
    prop: 'upInstNameAbbr',
    addDisplay: false,
      editDisplay:false,
      viewDisplay:false,
  },{
    label: '机构层级',
    prop: 'instLvl',
    type: 'select',
      hide: true,
      showColumn: true,
      dicData:[],
      rules: [{
        required: true,
        message: "请选择机构层级",
        trigger: "change"
      }]
  },{
    label: '机构类型',
    prop: 'instKind',
    type: 'select',
    dicData: [],
    rules: [{
      required: true,
      message: "请选择机构类型",
      trigger: "change"
    }]
  },
  {
    label: '机构状态',
    prop: 'orgStatus',
    type: "select",
    search: true,
    slot: true,
    formslot: true,
    dicData: [],
    rules: [{
      required: true,
      message: "请选择机构状态",
      trigger: "change"
    }]
  },{
    label: '机构类别',
    prop: 'brhType',
    formslot:true,
    hide: true,
    showColumn: false,
  },{
    label: '机构子类别',
    prop: 'brhSubType',
    formslot:true,
    hide: true,
    showColumn: false,
  },{
    label: '邮政编码',
    prop: 'instZipNo',
    hide: true,
    showColumn: false,
    rules: [{ min: 0, max: 6, message: '长度在 0 到 6 个字符', trigger: 'blur' },
    { validator: validate.isNumber, trigger: 'blur'}
  ]
  },{
    label: '机构负责人编号',
    prop: 'instInChargeTlrNo',
    hide: true,
    showColumn: false,
    rules: [{ min: 0, max: 11, message: '长度在 0 到 11 个字符', trigger: 'blur' },
    { validator: validate.roleIdValidate, trigger: 'blur'}
  ]
  },{
    label: '机构负责人姓名',
    prop: 'instInChargeName',
    hide: true,
    showColumn: false,
    rules: [{ min: 0, max: 64, message: '长度在 0 到 64 个字符', trigger: 'blur' }]
  },{
    label: '机构负责人电话',
    prop: 'instInChargePhone',
    hide: true,
    showColumn: false,
    rules: [{ min: 0, max: 11, message: '长度在 0 到 11 个字符', trigger: 'blur' },
    { validator: validate.telNumber, trigger: 'blur'}]
  },
  {
    label: '机构地址',
    prop: 'instAddress',
    type:"textarea",
    hide: true,
    showColumn: false,
    rules: [{ min: 0, max: 128, message: '长度在 0 到 128 个字符', trigger: 'blur' }]
  },{
    label: '创建人',
    prop: 'createUser',
    addDisplay: false,
      editDisplay:false,
      viewDisplay:false,
  },{
    label: '创建时间',
    prop: 'createTime',
    addDisplay: false,
      editDisplay:false,
      viewDisplay:false,
  },]
}
