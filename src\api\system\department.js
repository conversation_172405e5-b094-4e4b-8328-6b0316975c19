import request from "@/utils/request";

// 查询部门树
export function instTree(type) {
  return request({
    url: "/api/admin/sys/organ/tree/" + type,
    method: "get"
  });
}

// 查询部门部门树子节点
export function instDeptTreeChild(data) {
  return request({
    url: "/api/admin/sys/organ/child/nodes/tree",
    method: "post",
    data: data
  });
}

/**
 * 
 * 分页查询部门列表接口
 * @param {instId:部门编号,instNameAbbr:部门简称,upInstId:上级部门编号,pageNum：当前页,pageSize：每页条数}
 */
export function instsList(param) {
  const {
    currentPage = 1,
    pageSize = 10,
    deptId = "",
    deptName = "",
    deptStatus = "",
    instId = "",
    upDeptId = ""
  } = param || {};
  return request({
    url: `/api/admin/depts/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      deptId,
      deptName,
      deptStatus,
      instId,
      upDeptId
    }
  });
}
// 新增部门接口
export function addInsts(param) {
  const {
    deptId = "",
    deptName = "",
    displayOrder = 0,
    deptStatus = "",
    upDeptId = "",
    instId = ""
  } = param || {};
  return request({
    url: "/api/admin/depts/actions/add",
    method: "post",
    data: {
      deptId,
      deptName,
      displayOrder,
      deptStatus,
      upDeptId,
      instId
    }
  });
}

// 批量删除部门
export function delMoreInsts(data) {
  return request({
    url: "/api/admin/depts/actions/remove",
    method: "post",
    data: data
  });
}

// 修改更新部门接口
export function editInsts(param) {
  const {
    deptId = "",
    deptName = "",
    displayOrder = 0,
    deptStatus = "",
    upDeptId = "",
    instId = ""
  } = param || {};
  return request({
    url: "/api/admin/depts/actions/edit",
    method: "post",
    data: {
      deptId,
      deptName,
      displayOrder,
      deptStatus,
      upDeptId,
      instId
    }
  });
}

// 查询部门详情
export function getInsts(deptId) {
  return request({
    url: "/api/admin/depts/actions/get/" + deptId,
    method: "get"
  });
}

// 删除部门
export function delInsts(deptId) {
  return request({
    url: "/api/admin/depts/actions/remove/" + deptId,
    method: "post"
  });
}
//导出部门信息接口
export function exportInsts(data) {
  return request({
    url: "/api/admin/depts/actions/export",
    method: "post",
    data: data,
    responseType: "blob"
  });
}
//下载部门导入模板
export function downloadExample(data) {
  return request({
    url: "/api/admin/depts/template/actions/download",
    method: "get",
    data: data,
    responseType: "blob"
  });
}

//导入部门信息接口
export function importInsts(data) {
  return request({
    url: "/api/admin/depts/actions/import",
    method: "post",
    data: data,
    contentType: false,
    processData: false,
    headers: {
      "Content-Type": "multipart/form-data;"
    }
  });
}
//分页查询部门下用户接口
export function instsUserList(param) {
  const {
    currentPage = 1,
    pageSize = 10,
    deptId = "",
    userName = "",
    userNickname = "",
    userStat = ""
  } = param || {};
  return request({
    url: `/api/admin/depts/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      deptId,
      userName,
      userNickname,
      userStat
    }
  });
}
//分页查询非部门下用户接口
export function undeployUserList(param) {
  const {
    currentPage = 1,
    pageSize = 10,
    deptId = "",
    userName = "",
    userNickname = "",
    userStat = ""
  } = param || {};
  return request({
    url: `/api/admin/depts/no/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      deptId,
      userName,
      userNickname,
      userStat
    }
  });
}
//7.21保存部门下用户接口
export function addUserList(param) {
  return request({
    url: `/api/admin/depts/users/actions/add`,
    method: "post",
    data: param
  });
}

//删除部门下用户接口
export function delUserList(param) {
  return request({
    url: `/api/admin/depts/users/actions/remove`,
    method: "post",
    data: param
  });
}
