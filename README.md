# YOAF-Web 新权限管理系统

[![Vue](https://img.shields.io/badge/Vue-2.7.14-brightgreen.svg)](https://vuejs.org/)
[![Element UI](https://img.shields.io/badge/Element%20UI-2.15.6-blue.svg)](https://element.eleme.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 项目简介

YOAF-Web 是一个基于 Vue.js 的企业级金融管理系统，主要用于监管账户管理、批量交易处理、支付指令管理等业务场景。系统采用前后端分离架构，提供完整的权限管理和业务流程支持。

## 核心功能

- 🏦 **监管账户管理** - 账户信息的增删改查、审批流程
- 💰 **批量交易处理** - 批量交易控制、明细查询、状态管理
- 💳 **支付管理** - 支付指令处理、欠费库管理
- 📊 **对账清算** - 对账不一致处理、数据导出
- 👥 **权限管理** - 基于角色的权限控制系统
- 📋 **审计日志** - 完整的操作日志记录

## 技术栈

- **前端框架**: Vue.js 2.7.14
- **UI组件库**: Element UI 2.15.6
- **状态管理**: Vuex 3.6.0
- **路由管理**: Vue Router 3.4.9
- **HTTP客户端**: Axios 0.24.0
- **图表库**: ECharts 4.9.0
- **构建工具**: Vue CLI 4.4.6

## 快速开始

### 环境要求

- Node.js >= 8.9
- npm >= 3.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问地址: http://localhost:80

### 生产构建

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 项目结构

```
src/
├── api/                 # API接口定义
├── assets/             # 静态资源
├── components/         # 公共组件
├── directive/          # 自定义指令
├── layout/            # 布局组件
├── plugins/           # 插件配置
├── router/            # 路由配置
├── store/             # Vuex状态管理
├── utils/             # 工具函数
└── views/             # 页面组件
    ├── xzp/           # 新中平业务模块
    ├── user/          # 用户管理
    ├── role/          # 角色管理
    └── ...
```

## 主要业务模块

### 监管账户管理 (XZP)
- 账户信息的CRUD操作
- 账户状态批量管理
- 审批流程支持
- 数据导入导出

### 批量交易处理
- 批量交易控制台
- 交易明细查询
- 外联批次状态调整
- 交易异常处理

### 支付管理
- 支付指令管理
- 欠费库查询
- 敏感信息脱敏
- 支付状态跟踪

## 开发规范

- 遵循 Vue.js 官方风格指南
- 使用 ESLint 进行代码检查
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 提交前自动执行代码检查

## 部署说明

### 构建配置
- 生产环境路径: `/uepsxmzhbgweb`
- 支持 Gzip 压缩
- 代码分割优化
- 静态资源 CDN 支持

### 环境变量
- `NODE_ENV`: 环境标识
- `VUE_APP_BASE_API`: API基础路径
- `VUE_APP_TITLE`: 应用标题

## 文档

- 📖 [详细项目文档](./PROJECT_DOCUMENTATION.md)
- 🔧 开发指南
- 🚀 部署指南

## 更新日志

### v1.1.2 (2025-01-21)
- 优化监管账号表单验证体验
- 修复数据选择后校验信息不清除的问题
- 新增统一的表单验证清除方法
- 隐藏部分表单字段简化操作界面

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证

## 联系方式

- 开发团队: front-end
- 项目仓库: http://*************/root/yoaf-web-*******.git
- 问题反馈: 请通过 GitLab Issues 提交

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
