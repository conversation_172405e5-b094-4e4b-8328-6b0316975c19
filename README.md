# 厦门新中平资金监管系统 (XM-XZP)

[![Java](https://img.shields.io/badge/Java-1.8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.x-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-12+-blue.svg)](https://www.postgresql.org/)
![License](https://img.shields.io/badge/License-Proprietary-red.svg)

## 📋 项目简介

厦门新中平资金监管系统是一个基于Spring Boot的企业级资金监管平台，主要用于管理和监控资金账户信息、交易记录和业务操作。系统采用微服务架构，支持多数据源配置，提供完整的资金监管业务功能。

### 🎯 主要功能

- **账户信息管理**: 资金监管账户的查询、新增、修改等操作
- **交易记录管理**: 交易流水的记录、查询和统计分析
- **商户操作管理**: 商户业务操作的配置和管理
- **监管账户变动**: 监管账户变动情况的反馈和处理
- **数据统计分析**: 各类业务数据的统计和报表生成

## 🏗️ 系统架构

项目采用多模块Maven架构设计：

```text
xm-xzp/
├── xm-xzp-api/          # API接口定义模块
├── xm-xzp-impl/         # 业务实现模块
├── xm-xzp-boot/         # 启动模块
└── logs/                # 日志文件目录
```

### 技术栈

- **后端框架**: Spring Boot 2.x, Spring Cloud
- **数据库**: PostgreSQL 12+
- **ORM框架**: MyBatis-Plus 3.3.0
- **缓存**: Redis (通过YOAF框架集成)
- **服务发现**: Nacos
- **API文档**: Swagger 2
- **工具库**: Hutool, Lombok
- **分页组件**: PageHelper
- **数据源**: Dynamic DataSource (支持多数据源)

## 🚀 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- PostgreSQL 12+
- Redis 6.0+ (可选)
- Nacos 2.0+ (服务发现)

### 安装步骤

1. **克隆项目**

   ```bash
   git clone http://*************/root/xm-xzp-*******.git
   cd xm-xzp-*******
   ```

2. **配置数据库**

   创建PostgreSQL数据库并执行初始化脚本：

   ```sql
   CREATE DATABASE xm_xzp;
   -- 执行相关的建表脚本
   ```

3. **修改配置文件**

   编辑 `xm-xzp-boot/src/main/resources/application-dev.yml`：

   ```yaml
   spring:
     datasource:
       dynamic:
         datasource:
           master:
             url: ***************************************
             username: your_username
             password: your_password
           datasource2:
             url: *****************************************
             username: your_username
             password: your_password
   ```

4. **编译项目**

   ```bash
   mvn clean compile
   ```

5. **启动应用**

   ```bash
   cd xm-xzp-boot
   mvn spring-boot:run
   ```

6. **访问应用**
   - 应用地址: <http://localhost:8080>
   - Swagger文档: <http://localhost:8080/swagger-ui.html>

## 📖 API文档

### 核心接口

#### 账户信息管理

- **联想查询**: `GET /api/admin/xzp/suggestAcctInfo`
- **分页查询**: `POST /api/admin/xzp/queryAcctInfoList`
- **新增账户**: `POST /api/admin/xzp/addAcctInfo`

#### 商户操作管理

- **按商户分组**: `GET /api/admin/xzp/listGroupByMerchId`
- **按操作码分组**: `GET /api/admin/xzp/listGroupByOpeCd`

详细的API文档请参考：

- [账户信息查询接口说明](./账户信息查询接口说明.md)
- [账户信息管理接口文档](./账户信息管理接口文档.md)

## 🔧 配置说明

### 数据源配置

系统支持多数据源配置，主要数据源：

- `master`: 主数据源，用于核心业务数据
- `datasource2`: 第二数据源，用于监管账户相关数据

### 服务发现配置

使用Nacos作为服务注册与发现中心：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: your-nacos-server:8848
        namespace: xydpt-system
```

### 缓存配置

集成Redis缓存，支持多种缓存策略：

- 验证码缓存 (3-5分钟)
- 用户信息缓存 (1小时)
- 业务字典缓存 (1小时)

## 📁 项目结构

```text
src/
├── main/
│   ├── java/
│   │   └── com/xm/xzp/
│   │       ├── controller/     # 控制器层
│   │       ├── service/        # 服务层
│   │       ├── mapper/         # 数据访问层
│   │       ├── model/          # 数据模型
│   │       └── config/         # 配置类
│   └── resources/
│       ├── mapper/             # MyBatis映射文件
│       ├── application*.yml    # 配置文件
│       └── logback-spring.xml  # 日志配置
└── test/                       # 测试代码
```

## 🔍 开发指南

### 代码规范

- 使用Lombok简化代码
- 统一使用RestResponse包装返回结果
- 使用@DS注解指定数据源
- 使用@PMCTLLog记录审计日志

### 数据库操作

```java
@Service
public class ExampleServiceImpl {

    @DS("datasource2")  // 指定数据源
    public List<Entity> queryData() {
        // 数据库操作
    }
}
```

### 异常处理

系统使用统一的异常处理机制，所有业务异常都会被包装成RestResponse返回。

## 🧪 测试

运行单元测试：

```bash
mvn test
```

运行集成测试：

```bash
mvn verify
```

## 📊 监控与日志

### 日志配置

日志文件位于 `logs/` 目录下：

- `yoaf-log.log`: 应用主日志
- `yoaf-log-error.log`: 错误日志
- `yoaf-log-sql.log`: SQL执行日志
- `yoaf-log-warn.log`: 警告日志

### 审计日志

系统集成审计日志功能，自动记录关键业务操作。

## 🚀 部署

### 生产环境部署

1. 修改生产环境配置文件
2. 打包应用：

   ```bash
   mvn clean package -P prod
   ```

3. 部署到服务器：

   ```bash
   java -jar xm-xzp-boot/target/xm-xzp-boot-1.1.2.jar
   ```

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY xm-xzp-boot/target/xm-xzp-boot-1.1.2.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 版本历史

- **v1.1.2** (当前版本)
  - 完善账户信息管理功能
  - 优化数据库查询性能
  - 增强安全性配置

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。

## 📄 许可证

本项目为企业内部项目，版权归厦门新中平所有。

---

**注意**: 本项目基于PSBC PFPJ开发平台构建，集成了统一认证、审计日志、缓存管理等企业级功能组件。
