<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" class="search-form" label-width="100px" size="small">
      <el-row :gutter="24">
        <el-col :span="10" style="display: flex;align-items: center;">
          <el-form-item label="业务代码">
            <el-autocomplete
              v-model="search.opeCd"
              :fetch-suggestions="queryOpeCd"
              placeholder="请输入业务代码"
              @select="handleOpeCdSelect"
              @clear="handlerOpeCdClear"
              :trigger-on-focus="true"
              style="width: 200px;"
              clearable
            >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value +'-'+ item.item.opeNm }}</div>
              </div>
            </template>
            </el-autocomplete>
          </el-form-item>
          <span class="tip-text">{{ search.opeNm }}</span>
        </el-col>
        <el-col :span="12" style="display: flex;align-items: center;">
          <el-form-item label="委托单位代码">
            <el-autocomplete
              v-model="search.merchId"
              :fetch-suggestions="queryMerchId"
              placeholder="请输入委托单位代码"
              @select="handleMerchIdSelect"
              @clear="handlerMerchIdClear"
              :trigger-on-focus="true"
              style="width: 200px;"
              clearable
            >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.item.prdtNm}}</div>
              </div>
            </template>
            </el-autocomplete>
          </el-form-item>
          <span class="tip-text">{{ search.merchNm }}</span>
        </el-col>
        
        
      </el-row>
      <el-row :gutter="24" style="margin-top: 0;">
        <el-col :span="10">
          <el-form-item label="起始日期">
            <el-date-picker style="width: 200px;" v-model="search.startTime" type="date" value-format="yyyyMMdd" placeholder="请选择起始日期" clearable  @change="validateEndTime"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止日期">
            <el-date-picker style="width: 200px;" v-model="search.endTime" type="date" value-format="yyyyMMdd" placeholder="请选择终止日期" clearable  @change="validateEndTime"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" style="margin-top: 0;">
        <el-col :span="10">
          <el-form-item label="外联批次号">
            <el-input v-model="search.batchId" clearable placeholder="请输入外联批次号" style="width: 200px;"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>  
        <el-col :span="24" class="search-btns">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    </el-card>
    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      :before-open="beforeOpen"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
    <template slot-scope="{row,size,type}" slot="menu">
        <el-button
            :size="size"
            :type="type"
            @click="handleCommand(row)"
            icon="el-icon-info"
          >查看</el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { batchProcessLogList, batchUpdateTxnStaToZero } from '@/api/batchprocesslog';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import batchProcessLogOption from './infoData/batchProcessLogOption.js';


export default {
  name: 'batchprocesslog',
  data() {
    return {
      loading: false,
      formParent: {},
      search: {
        startTime: '',
        endTime: '',
        opeCd: '',
        matchId: '',
        batchId: ''
      },
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      data: [],
      option: batchProcessLogOption,
      multiple: true,
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false,
    };
  },
  created() {
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    onLoad() {
      this.getList();
    },
    beforeOpen(done, type) {
      done();
    },
    handleCommand(row) {
      this.$refs.crud.rowView(row);
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    async getList() {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      try {
        const { code, data } = await batchProcessLogList(params);
        if (code === '0') {
          this.data = data.list;
          this.page.total = data.total;
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },

    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opeCd
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 清楚方法
    handlerOpeCdClear(){
      this.search.opeCd = ''
      this.search.opeNm = ''
    },
    handlerMerchIdClear(){
      this.search.merchId = ''
      this.search.merchNm = ''
    },

    // 选择业务代码
    handleOpeCdSelect(item) {
      this.search.opeCd = item.value;
      this.search.opeNm = item.item.opeNm;
      // 清空委托单位代码，触发重新查询
      if (this.search.merchId) {
        this.search.merchId = '';
        this.search.merchNm = '';
      }
    },

    // 选择委托单位代码
    handleMerchIdSelect(item) {
      this.search.merchId = item.value;
      this.search.merchNm = item.item.prdtNm;
      // 如果没有业务代码，可以根据选择的委托单位代码查询相关的业务代码
      if (!this.search.opeCd) {
        // 可以在这里添加查询逻辑

      }
    },

    handleQuery() {
      this.page.currentPage = 1;
      this.getList();
    },
    resetQuery() {
      this.search = {
        startTime: '',
        endTime: '',
        opeCd: '',
        prdtNm: '',
        matchId: '',
        batchId: '',
        merchNm: ''
      };
      this.opeCdOptions = [];
      this.merchIdOptions = [];
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.multiple = !(selection.length > 0);
    },
    validateEndTime() {
      if (this.search.startTime && this.search.endTime) {
        if (Number(this.search.endTime) < Number(this.search.startTime)) {
          this.$message.warning('终止日期不能小于起始日期');
          this.search.endTime = '';
        }
      }
    },
  },
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
  /* padding-right: 10px; */
}
.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 