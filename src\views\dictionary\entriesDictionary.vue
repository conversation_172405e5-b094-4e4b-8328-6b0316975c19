<template>
  <div>
    <div class="grid-content bg-purple">
      <div class="app-container">
        <yo-table
          v-loading="tableLoading"
          :data="data"
          :option="option"
          ref="cruds"
          :page.sync="page"
          :search.sync="search"
          :before-open="beforeOpen"
          @search-change="searchChange"
          @on-load="onLoad"
          @row-save="rowSave"
          @row-del="moreRowDel"
          @row-update="rowUpdate"
          @selection-change="selectionChange"
          v-model="formParent"
        >
          <template slot-scope="{scope}" slot="searchMenu">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-search"
              @click.stop="handleQuery()"
              v-hasPermi="['admin:dict:entries:page']"
              style="marginLeft: 16px;"
            >查询</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
          </template>
          <!-- 自定义左侧操作栏 -->
          <template slot-scope="{size}" slot="menuLeft">
            <el-button
              type="primary"
              icon="el-icon-plus"
              :size="size"
              plain
              v-hasPermi="['admin:dict:entries:add']"
              @click.stop="handleAdd()"
            >新增</el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              :size="size"
              plain
              v-hasPermi="['admin:dict:entries:remove:batch']"
              @click.stop="handleDel()"
            >批量删除</el-button>
          </template>
          <!-- 自定义列 -->
          <template slot="status" slot-scope="scope">
            <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{status(scope.row)}}</el-tag>
          </template>
          <!-- 自定义操作按钮 -->
          <template slot-scope="{row,size,type,index}" slot="menu">
            <el-button
              :size="size"
              :type="type"
              icon="el-icon-edit"
              @click.stop="handleList(row,index)"
              v-hasPermi="['admin:dict:entries:edit']"
            >修改</el-button>
            <el-button
              :size="size"
              :type="type"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              v-hasPermi="['admin:dict:entries:remove']"
            >删除</el-button>
          </template>
        </yo-table>
      </div>
    </div>
  </div>
</template>
<script>
import {
  entriesList,
  delEntries,
  addEntries,
  delMoreEntries,
  updateEntries
} from "@/api/system/dictionary";
import entriesOption from "./infoData/entriesOption.js";
export default {
  name: "entriesDictionary",
  components: {},
  dicts: ["yoaf_dict_entries_state"],
  data() {
    return {
      showContainer: true, //显示主界面
      formParent: {},
      search: {},
      dictTypeId:"",
      page: {
        layout: "total,prev,pager,next",
        pageSize: 10,
        currentPage: 1,
        pagerCount:5
      },
      data: [],
      option: entriesOption,
      // 遮罩层
      loading: true,
      tableLoading: true,
      // 表单参数
      checkPageAll: false, // 操作权限弹窗中全选
      isIndeterminate: false, // 全选的不确定状态 只要有一个子集不为true 即设置true
      delMoreArr: [] //批量删除数组
    };
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "status",
      this.dict.type["yoaf_dict_entries_state"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    /** 查询字典列表 */
    getList(params) {
      const data = { dictTypeId:this.dictTypeId, ...this.page, ...params };
      entriesList(data).then(response => {
        const res = response.data;
        this.data = res.list;
        this.page.total = res.total;
        this.tableLoading = false;
      });
    },
    beforeOpen(done, type) {
      if (["view", "edit"].includes(type)) {
        // 查看和编辑逻辑
      } else {
        // 新增逻辑
        // 一定要用setTimeout包裹，由于form组件底层一些机制的设计
        setTimeout(() => {
          this.formParent.dictTypeId = this.dictTypeId;
        }, 0);
      }
      done();
    },
    //新增按钮操作
    handleAdd() {
      this.$refs.cruds.rowAdd();
    },
    //删除按钮操作
    handleDel() {
      this.$refs.cruds.rowDel();
    },
    //多选字典
    selectionChange(list) {
      this.delMoreArr = [];
      list.forEach(e => {
        let item = {
          dictId: e.dictId,
          dictTypeId: e.dictTypeId
        };
        this.delMoreArr.push(item);
      });
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      addEntries(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList(this.search);
            done();
          } 
        })
        .catch(error => {
          loading();
        });
    },
    //批量删除表单
    moreRowDel(form, done, loading) {
      if (this.delMoreArr.length > 0) {
         this.$modal
        .confirm('是否确认删除所选的数据项？')
        .then(()=> {
          delMoreEntries(this.delMoreArr)
          .then(response => {
            if (response.code == 0) {
              this.$modal.msgSuccess(response.message);
              this.getList(this.search);
              done();
            } else {
              loading();
            }
          })
          .catch(error => {
            loading();
          });
        })
      } else {
        this.$modal.msgWarning("请选择删除数据！");
      }
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      updateEntries(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList(this.search);
            done();
          } 
        })
        .catch(error => {
          loading();
        });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 搜索按钮操作 */
    handleQuery(dictTypeId) {
      if(dictTypeId)this.dictTypeId = dictTypeId;
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.cruds.searchReset();
    },
    /** 修改按钮操作 */
    handleList(row, index) {
      this.formParent = row;
      this.$refs.cruds.rowEdit(row, index);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictTypeId = row.dictTypeId;
      const dictId = row.dictId;
      this.$modal
        .confirm('是否确认删除字典项代码为"' + dictId + '"的数据项？')
        .then(function() {
          return delEntries(dictTypeId, dictId);
        })
        .then(res => {
          if (res.code == 0) {
            this.getList(this.search);
            this.$message.success(res.message);
          }
        })
        .catch(() => {});
    },
    status(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "3":
          return "注销";
        case "4":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
</style>