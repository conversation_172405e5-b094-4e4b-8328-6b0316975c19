package com.xm.xzp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 配置RestTemplate Bean
     * 用于HTTP请求转发
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 设置连接超时时间（毫秒）
        factory.setConnectTimeout(30000);
        
        // 设置读取超时时间（毫秒）
        factory.setReadTimeout(60000);
        
        return new RestTemplate(factory);
    }
}
