<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
export default {
  props: {
    // 菜单地址，路由或外链地址
    to: {
      type: [String, Object],
      required: true
    },
    // 菜单类型，默认值2（路由菜单）
    menuType: {
      type : String,
      default: '2'
    }
  },
  computed: {
    // 菜单组件类型，a-外链，router-link-路由地址
    type() {
      if (this.menuType === '3') {
        return 'a'
      }
      return 'router-link'
    }
  },
  methods: {
    /**
     * <a> 标签参数
     *
     * @param to
     * @returns {{to: *}|{rel: string, href: *, target: string}}
     */
    linkProps(to) {
      if (this.menuType === '3') {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        }
      }
      return {
        to: to
      }
    }
  }
}
</script>
