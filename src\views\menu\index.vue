<template>
  <el-container>
    <el-aside>
      <yo-tree
        v-loading="treeLoading"
        :option="treeoption"
        :data="treedata"
        @node-click="nodeClick"
        v-model="form"
        style="height: 85vh"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span class="custom-tree-node-title" :title="node.label">
            <svg-icon :icon-class="data.icon" />
            {{ node.label }}</span
          >
          <span>
            <el-button
              type="text"
              circle
              icon="el-icon-edit"
              size="mini"
              @click.stop="() => append(data)"
              v-hasPermi="['admin:menu:edit']"
            >
            </el-button>
            <el-button
              type="text"
              circle
              icon="el-icon-delete"
              size="mini"
              style="color: #f56c6c"
              @click.stop="() => remove(node, data)"
              v-hasPermi="['admin:menu:remove']"
            >
            </el-button>
          </span>
        </span>
        <template slot-scope="scope" slot="addBtn">
          <!-- <el-button type="primary" @click="handdleAdd(scope)" icon="el-icon-plus"></el-button> -->
          <el-button
            style="color: #409eff"
            type="primary"
            @click="handdleAdd(scope)"
            icon="el-icon-plus"
            v-hasPermi="['admin:menu:add']"
            >新增</el-button
          >
        </template>
      </yo-tree>
    </el-aside>
    <el-main style="padding-top:0px;" >
      <el-tabs>
        <el-tab-pane class="app-container">
          <span slot="label">
            <svg-icon v-if="menuName.icon" :icon-class="menuName.icon" />
            {{ menuName.menuName }}</span
          >
          <yo-table
            v-loading="tableLoading"
            :data="tabledata"
            :option="tableoption"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
            @search-change="searchChange"
          >
            <template slot-scope="scope" slot="menuIdForm">
              <el-cascader
                ref="menuIdFormRef"
                :disabled="disabled"
                v-model="formParent.menuId"
                :options="treeList"
                clearable
                :props="{ value: 'id', label: 'menuName', checkStrictly: true }"
                placeholder="请选择所属菜单"
                @change="closeMenuIdCascader()"
              >
              </el-cascader>
            </template>
            <template slot-scope="scope" slot="displayOrderForm">
              <el-input-number
                v-model="formParent.displayOrder"
                step-strictly
                controls-position="right"
                :min="0"
                :max="10000"
                style="width: 100%"
              >
              </el-input-number>
            </template>
            <template slot-scope="scope" slot="funcURIForm">
              <el-input
                placeholder="请输入请求地址"
                v-model.trim="funcURI"
                class="input-with-select"
              >
                <el-select
                  v-model="funcURIMethod" slot="prepend"
                  placeholder="请选择"
                  style="width: 100px;"
                >
                  <el-option
                    v-for="dict in dict.type['yoaf_request_type']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-input>
            </template>
            <template slot-scope="{ scope }" slot="searchMenu">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-search"
                @click.stop="handleQuery"
                v-hasPermi="['admin:function:page']"
              >
                查询
              </el-button>
              <el-button
                size="mini"
                icon="el-icon-refresh"
                @click.stop="resetQuery()"
                >重置
              </el-button>
            </template>
            <!-- 左侧操作栏 -->
            <template slot-scope="{ size }" slot="menuLeft">
              <el-button
                type="primary"
                icon="el-icon-plus"
                :size="size"
                plain
                @click.stop="handleAdd"
                v-hasPermi="['admin:function:add']"
                >新增功能</el-button
              >
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                :size="size"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['admin:function:remove:batch']"
                >批量删除</el-button
              >
            </template>
            <template slot-scope="{ row, size, type, index }" slot="menu">
              <el-button
                :size="size"
                :type="type"
                icon="el-icon-edit"
                @click.stop="handleEdit(row, index)"
                v-hasPermi="['admin:function:edit']"
              >
                修改
              </el-button>
              <el-button
                :size="size"
                :type="type"
                icon="el-icon-delete"
                @click="handleDelete(row)"
                v-hasPermi="['admin:function:remove']"
              >
                删除
              </el-button>
            </template>
          </yo-table>
        </el-tab-pane>
      </el-tabs>
    </el-main>

    <!-- 菜单表单弹框 -->
    <p-dialog
      ref="userRegistry"
      @cancel="cancel"
      @submit="submit"
      :title="title"
      class="menu-form-dialog"
    >
      <template slot="content">
        <el-form
          :model="treeForm"
          ref="treeForm"
          :rules="rules"
          label-suffix=":"
          label-width="120px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="系统编号" prop="systemCode">
                <el-select
                  v-model="treeForm.systemCode"
                  placeholder="系统编号"
                  @change="changeItem($event)"
                  clearable
                  style="width: 300px"
                >
                  <el-option
                    v-for="dict in dict.type['yoaf_system_code']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="parentId">
                <span slot="label">
                  <el-tooltip
                    content="选择菜单所属结构，注意：更新菜单时，不能选择自己"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  上级菜单
                </span>
                <el-cascader
                  v-model="treeForm.parentId"
                  :options="treeFilterData"
                  :key="Math.random()"
                  :props="{
                    value: 'id',
                    label: 'menuName',
                    checkStrictly: true,
                    expandTrigger: 'hover'
                  }"
                  clearable
                  style="width: 300px"
                  placeholder="请选择上级菜单"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="菜单名称" prop="menuName">
                <el-input
                  v-model.trim="treeForm.menuName"
                  clearable
                  placeholder="请输入菜单名称"
                  style="width: 300px"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="菜单图标" prop="icon">
                <el-popover
                  placement="bottom-start"
                  width="460"
                  trigger="click"
                  @show="$refs['iconSelect'].reset()"
                >
                  <IconSelect ref="iconSelect" @selected="selected" />
                  <el-input
                    slot="reference"
                    v-model="treeForm.icon"
                    placeholder="点击选择图标"
                    clearable
                    style="width: 300px"
                  >
                    <svg-icon
                      slot="prefix"
                      :icon-class="treeForm.icon ? treeForm.icon : ''"
                      class="el-input__icon"
                      style="height: 32px; width: 16px"
                    />
                  </el-input>
                </el-popover>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row >
            <el-col :span="12">
              <el-form-item label="菜单类型" prop="menuType">
                  <el-radio-group v-model="treeForm.menuType" >
                  <el-radio v-for="dict in dict.type['yoaf_menu_type']"
                  :key="dict.value"
                  :label="dict.value"
                  @change="changeMenuFormMenuType($event)"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
                <!-- <yo-radio v-model="treeForm.menuType" :dic="dic"></yo-radio> -->
              </el-form-item>
            </el-col>
            <el-col v-if="treeForm.menuType === '3'" :span="12">
              <el-form-item label="外链地址" prop="menuUrl">
                  <el-input
                  v-model.trim="treeForm.menuUrl"
                  clearable
                  placeholder="请输入外链地址"
                  style="width: 300px"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="treeForm.menuType === '1'">
              <el-form-item prop="routeUrl">
                <span slot="label">
                  <el-tooltip
                    content="访问的路由地址，如：`menu`，请勿以 【 / 】开头"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路由地址
                </span>
                <el-input
                  v-model.trim="treeForm.routeUrl"
                  clearable
                  style="width: 300px"
                >
                  <template slot="prepend">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      :open-delay="500"
                      :content="routeUrl"
                    >
                      <span slot="reference">{{ routeUrl | ellipsis }}/</span>
                    </el-popover>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" v-if="treeForm.menuType === '2'">
              <el-form-item prop="routeUrl">
                <span slot="label">
                  <el-tooltip
                    content="访问的路由地址，如：`menu`，请勿以 【 / 】开头"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路由地址
                </span>
                <el-input
                  v-model.trim="treeForm.routeUrl"
                  clearable
                  style="width: 300px"
                >
                  <template slot="prepend">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      :open-delay="500"
                      :content="routeUrl"
                    >
                      <span slot="reference">{{ routeUrl | ellipsis }}/</span>
                    </el-popover>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col v-if="treeForm.menuType === '2'" :span="12">
              <el-form-item prop="componentUrl">
                <span slot="label">
                  <el-tooltip
                    content="访问的组件路径，如：`menu/index`，默认在`views`目录下"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  组件路径
                </span>
                <el-input
                  v-model.trim="treeForm.componentUrl"
                  clearable
                  style="width: 300px"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="treeForm.status"
                  placeholder="状态"
                  clearable
                  style="width: 300px"
                >
                  <el-option
                    v-for="dict in dict.type['yoaf_menu_state']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="displayOrder">
                <el-input-number
                  v-model="treeForm.displayOrder"
                  step-strictly
                  controls-position="right"
                  :min="0"
                  :max="10000"
                  style="width: 300px"
                >
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </p-dialog>
  </el-container>
</template>
<script>
import {
  listMenu,
  getMenu,
  addMenu,
  updateMenu,
  delMenu,
  delTable,
  delTables,
  addTable,
  getOneTable,
  editOneTable,
} from "@/api/system/menu";
import treeoption from "./infoData/treeoption.js";
import tableoption from "./infoData/tableoption.js";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  dicts: ["yoaf_menu_type", "yoaf_system_code", "yoaf_menu_state", "yoaf_request_type"],

  components: { IconSelect },
  data() {
    var checkUrl = (rule, value, callback) => {
      if (value && /^(\/)/i.test(value)) {
        callback(new Error("请勿以 / 开头"));
      }
      callback();
    };
    return {
      title: "",
      treeLoading: true,
      tableLoading: true,
      rules: {
        systemCode: [
          { required: true, message: "请选择系统编号", trigger: "blur,change" },
        ],
        parentId: [
          { required: true, message: "请选择上级菜单", trigger: "blur,change" },
        ],
        menuName: [
          { required: true, message: "请输入菜单名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        menuType: [
          { required: true, message: '请选择菜单类型', trigger: '' }
        ],
        icon: [
          { required: false, message: "请选择菜单图标", trigger: "blur,change" },
        ],
        displayOrder: [
          { required: true, message: "请输入排序", trigger: "change" },
        ],
        menuUrl: [
          { required: true, message: "请输入外链地址", trigger: "blur" },
        ],
        routeUrl: [
          { required: true, message: "请输入路由地址", trigger: "blur" },
          {
            min: 1,
            max: 150,
            message: "长度在 1 到 150 个字符",
            trigger: "blur",
          },
          { validator: checkUrl, trigger: "blur" },
        ],
        componentUrl: [
          { required: true, message: "请输入菜单URL", trigger: "blur" },
          {
            min: 1,
            max: 150,
            message: "长度在 1 到 150 个字符",
            trigger: "blur",
          },
          { validator: checkUrl, trigger: "blur" },
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "blur,change" },
        ],
      },
      form: {},
      formParent: {},
      funcURI: "", //funcURI路径
      funcURIMethod: "", //funcURI请求方式
      search: {},
      treedata: [],
      treeFilterData: [],
      treeoption: treeoption,
      treeForm: {
        menuUrl: '', // 外链地址
        menuType: '', // 菜单类型
        systemCode: "",
        parentId: "",
        status: "1",
        displayOrder: 0,
        menuName: "",
        icon: "",
        routeUrl: "",
        componentUrl: "",
      },
      treeList: [],
      disabled: false,
      // 选中菜单树节点数据
      selectedMenuTreeNode: {},
      menuId: "",
      menuName: {
        menuName: "全部",
        icon: "",
      },
      routeUrl: "",
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      tabledata: [],
      tableoption: tableoption,
      // 非多个禁用
      multiple: true,
      dic: []
    };
  },
  filters: {
    ellipsis(value) {
      if (!value) return "";
      if (value.length > 10) {
        return value.slice(0, 10) + "...";
      }
      return value;
    },
  },
  computed: {
    // 功能url由两个变量拼接
    funcUrl: {
      get() {
        return this.funcURIMethod + ":" + this.funcURI;
      },
      set(newval) {
        const funcUrls = newval.split(":");
        this.funcURIMethod = funcUrls[0];
        this.funcURI = funcUrls[1];
      },
    },
    componentUrlIsShow() {
      //this.treeForm.parentId为空和-1时，不显示菜单URL
      if (Array.isArray(this.treeForm.parentId)) {
        return this.treeForm.parentId.length ? true : false;
      } else {
        if (!this.treeForm.parentId || this.treeForm.parentId === -1) {
          return false;
        }
        return true;
      }
    }
  },
  watch: {
    "treeForm.parentId": {
      handler(newval) {
        /*
          2022/4/13
            parentId 为数组时，需要循环遍历获取的值
            有值并且值不等于-1时，不用循环遍历，但需要从树中查找routeUrl
            为空或者-1时，routeUrl直接为空
          */
        if (Array.isArray(this.treeForm.parentId)) {
          if (newval.length) {
            let str = "";
            newval.forEach((item) => {
              str = str + this.forTreeData(this.treedata, item);
            });
            if (/^(\/\/)/i.test(str)) {
              str = str.substr(1);
            }
            this.routeUrl = str;
            // console.log(this.treeForm.componentUrl, 'componentUrl>>>>>>>>>>>>')
            if (this.treeForm.componentUrl === "Layout") {
              this.treeForm.componentUrl = "";
            }
          } else {
            this.routeUrl = "";
          }
        } else {
          if (newval && newval != -1) {
            let str = "";
            str = str + this.forTreeData(this.treedata, newval);
            if (/^(\/\/)/i.test(str)) {
              str = str.substr(1);
            }
            this.routeUrl = str;
          } else {
            this.routeUrl = "";
          }
        }
      },
      deep: true,
    },
  },
  created() {
    this.getMenuList();
    this.updateDictData(
      this.tableoption.column,
      "funcURI",
      this.dict.type["yoaf_request_type"]
    );
  },
  mounted() {
    this.dic = this.dict.type["yoaf_menu_type"]
  },
  methods: {
    // 递归过滤菜单树
    filterMenuTreeData(data, sysCode) {
      return data.filter(item => {
        if (item.systemCode === sysCode && item.menuType === '1') {
          if (item.children && item.children.length > 0) {
            item.children = this.filterMenuTreeData(item.children, sysCode)
            // 无子菜单则删除children属性
            if (item.children.length === 0) {
              delete item['children']
            }
          }
          return true
        }
        return false
      })
    },
    // 新增/编辑菜单时，系统编号改变则 上级菜单 的数据根据系统编号和目录过滤
    changeItem(sysCode) {
      if (sysCode) {
        this.treeFilterData = this.filterMenuTreeData(JSON.parse(JSON.stringify(this.treedata)), sysCode)
      } else {
        this.treeFilterData = []
        // 联动所属菜单
        this.treeForm.parentId = ''
      }
    },
    // 新增/编辑菜单时, 菜单类型改变 则 上级菜单 是否必填进行变化
    changeMenuFormMenuType(menuType) {
      if (menuType === '2' || menuType === '3') {
        this.rules.parentId = [ { required: true, message: "请选择上级菜单", trigger: "blur,change" } ]
      } else {
        this.rules.parentId = [ { required: false, message: "请选择上级菜单", trigger: "blur,change" } ]
      }
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    // table方法
    /** 搜索按钮操作 */
    handleQuery() {
      const search = { menuId: this.menuId, ...this.search };
      this.page.currentPage = 1;
      this.getMenuTable(search);
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.formParent = {
        displayOrder: 0,
        menuId: this.menuName.id
      };
      this.funcUrl = "";
      this.disabled = false;
      this.$refs.crud.rowAdd();
    },
    // 功能表单，选择上级菜单后自动关闭级联框
    closeMenuIdCascader() {
      this.$refs.menuIdFormRef.dropDownVisible = false
    },
    // [功能列表]-多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.names = selection.map((item) => item.funcName);
      this.multiple = !selection.length;
    },
    // [功能列表]-删除按钮操作
    handleDelete(row) {
      const id = row.id || this.ids;
      const name = row.funcName || this.names
      const h = this.$createElement;
      this.$confirm(`是否确认删除功能名称为" ${name} "的数据项？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        showCancelButton: true,
      })
        .then(() => {
          if (row.id) {
            this.handleDel(id, delTable);
          } else {
            this.handleDel(id, delTables);
          }
        })
        .catch(() => {});
    },
    async handleDel(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getMenuTable({ menuId: this.menuId });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$refs.crud.rowExcel();
    },
    /** 导入按钮操作 */
    handleImport() {
      console.log("导入");
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      const menuid = this.formParent.menuId;
      if (typeof menuid !== "string") {
        form.menuId = menuid[menuid.length - 1];
      }
      form.funcURI = this.funcUrl;
      addTable(form).then((response) => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
          done();
        }
      });
    },
    // 修改
    handleEdit(row, index) {
      this.disabled = true;
      getOneTable(row.id).then((response) => {
        this.formParent.menuId = response.data.menuId;
        this.formParent = response.data;
        this.funcUrl = response.data.funcURI;
        this.$refs.crud.rowEdit(response.data, index);
      });
    },
    // [功能表单]-修改表单保存
    rowUpdate(form, index, done, loading) {
      form.funcURI = this.funcUrl;
      editOneTable(form).then((response) => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
          done();
        }
      });
    },
    // 刷新按钮
    refresh(val) {
      this.handleQuery();
    },
    onLoad(page) {
      this.getMenuTable({ menuId: this.menuId, ...this.search });
    },
    // 分页查询菜单功能列表
    getMenuTable(query) {
      const data = { ...query, ...this.page };
      getMenu(data).then((res) => {
        this.page.total = res.data.total;
        this.tabledata = res.data.list;
        this.tableLoading = false;
      });
    },
    // 选择菜单图标
    selected(name) {
      this.treeForm.icon = name;
    },
    /** 查询菜单列表 */
    getMenuList() {
      listMenu().then((response) => {
        this.treedata = response.data;
        this.treeList = response.data;
        this.treeLoading = false;
      });
    },
    optionChange(data) {},
    //点击新增菜单
    handdleAdd(data) {
      this.treeForm = {
        menuType: '',
        systemCode: "yoaf",
        parentId: "-1",
        status: "1",
        displayOrder: 0,
        menuName: "",
        icon: "",
        routeUrl: "",
        componentUrl: "",
      };
      // 联动选中树节点数据
      if (this.selectedMenuTreeNode) {
        this.treeForm.systemCode = this.selectedMenuTreeNode.systemCode

        if (this.selectedMenuTreeNode.menuType === '1') {
          this.treeForm.parentId = this.selectedMenuTreeNode.id;
        } else {
          this.treeForm.parentId = this.selectedMenuTreeNode.parentId;
        }
      }
      this.changeItem(this.treeForm.systemCode);
      this.changeMenuFormMenuType(this.treeForm.menuType)
      this.title = "新增菜单";
      this.$refs.userRegistry.show();
    },
    // 点击修改
    append(node, data) {
      const nodeData = JSON.parse(JSON.stringify(node))
      this.title = "修改菜单";
      this.$refs.userRegistry.show();
      nodeData.parentId = nodeData.parentId == "-1" ? "" : nodeData.parentId;
      let obj = {
        ...node,
      };
      if (!nodeData.parentId || nodeData.parentId == "-1") {
        obj.routeUrl = obj.routeUrl.substr(1);
      }
      obj.menuType = nodeData.menuType
      this.treeForm = obj;
      this.changeItem(nodeData.systemCode);
      this.changeMenuFormMenuType(nodeData.menuType);
    },
    // 取消
    cancel() {
      this.$refs.treeForm.resetFields();
      // this.getMenuList();
      this.$refs.userRegistry.close();
      this.treeFilterData = []
    },
    // 新增 修改 保存
    /**
     * 2022/4/12
     * 新增和修改时，若没有选择上级菜单,则
     *  菜单URL（即组件路径）不显示，默认提交Layout
     *  路由路径填写时不能以/开头，但提交给后端数据时，添加上/
     * 如果选择了上级菜单,则
     *  菜单URL自己填写，不能以/开头
     *  路由路径不能以/开头，提交给后端数据时，不添加/
     *
     *  **/
    submit() {
      const {
        menuUrl = '',
        menuType = "",
        systemCode = "",
        id = "",
        menuName = "",
        routeUrl = "",
        componentUrl = "",
        parentId = "",
        displayOrder = 0,
        icon = "",
        status = "",
      } = this.treeForm || {};
      let parentStr = "",
        componentUrlStr = "",
        routerUrlStr = "";
      if (Array.isArray(parentId)) {
        parentStr = parentId[parentId.length - 1];
      } else {
        parentStr = parentId;
      }
      if (!parentStr || parentStr == -1) {
        // componentUrlStr = "Layout";
        componentUrlStr = componentUrl;
        routerUrlStr = "/" + routeUrl;
      } else {
        componentUrlStr = componentUrl;
        routerUrlStr = routeUrl;
      }
      let submitData = {
        menuUrl,
        menuType,
        // menuType,
        systemCode,
        id,
        menuName,
        routeUrl: routerUrlStr,
        componentUrl: componentUrlStr,
        parentId: parentStr ? parentStr : "-1",
        displayOrder,
        icon,
        status,
      };
      this.$refs.treeForm.validate((valid) => {
        if (valid) {
          if (submitData.id == submitData.parentId) {
            this.$message.error("上级菜单不能选择自己");
            return;
          }
          return this.treeForm.id
            ? this.update(submitData)
            : this.save(submitData);
        } else {
          return false;
        }
      });
    },
    // 点击节点回调
    nodeClick(data, node) {
      if (data) {
        // 选中节点数据
        this.selectedMenuTreeNode = JSON.parse(JSON.stringify(data))
      }
      const query = { menuId: data.id };
      this.menuId = data.id;
      this.menuName = {
        menuName: data.menuName,
        icon: data.icon,
        id: data.id
      };
      this.page.currentPage = 1;
      return this.getMenuTable(query);
    },
    // 修改保存
    update(form) {
      updateMenu(form).then((res) => {
        if (res.code == 0) {
          this.$refs.userRegistry.close();
          this.getMenuList();
          this.$message.success(res.message);
        }
      });
    },
    // 新增保存
    save(data) {
      addMenu(data).then((res) => {
        if (res.code == 0) {
          this.$refs.userRegistry.close();
          this.getMenuList();
          this.$message.success(res.message);
        }
      });
    },
    // 删除菜单
    remove(node, data) {
      const h = this.$createElement;
      this.$confirm(`此操作将永久删除" ${data.menuName} "是否继续？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        showCancelButton: true,
      })
        .then(() => {
          this.handleDelUser(data.id, delMenu);
        })
        .catch(() => {});
    },
    async handleDelUser(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getMenuList();
        this.getMenuTable();
        (this.menuName = {
          menuName: "全部",
          icon: "",
        })
      }
    },
    // 遍历treedata数据
    forTreeData(data, val) {
      let str = "";
      let fn = (data) => {
        for (let x of data) {
          if (x.id === val) {
            str = str + "/" + x.routeUrl;
          }
          if (x.children) {
            fn(x.children);
          }
        }
      };
      fn(data);
      return str;
    },
    //过滤列表数据
    filterData(data) {
      var _filterData = (ele) => {
        if (ele && ele.length) {
          ele.forEach((item) => {
            if (item.componentUrl == "Layout") {
              item.disabled = true;
            }
          });
        }
      };
      _filterData(data);
      return data;
    },
  },
};
</script>
<style scoped lang="scss">
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: 210px;
}
.custom-tree-node-title {
  display: inline-block;
  min-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-tree {
  min-height: calc(100vh - 200px) !important;
}
</style>
<style scoped>
.input-with-select >>> .el-input {
  width: 82px !important;
}
.input-with-select >>> .el-select .el-input {
  width: 82px !important;
}
.input-with-select >>> .el-select .el-input .el-input__inner{
  width: 122px !important;
}
/* .input-with-select >>>.el-input-group__prepend {
    background-color: #fff;
  } */
</style>
<style scoped>
/* 菜单表单弹框最大化对齐顶部 */
.menu-form-dialog >>> .el-dialog {
  margin-top: 0vh !important;
}
</style>
