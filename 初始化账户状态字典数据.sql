-- 初始化账户状态字典数据
-- 用于账户管理页面的状态查询下拉框

-- 1. 插入字典类型
INSERT INTO dict_type (dict_type_id, dict_type_name, data_source, status, remark, create_time, update_time)
VALUES ('xzp_acct_status', '账户状态', 'SYSTEM', '1', '账户管理页面状态查询使用', NOW(), NOW())
ON CONFLICT (dict_type_id) DO UPDATE SET
    dict_type_name = EXCLUDED.dict_type_name,
    remark = EXCLUDED.remark,
    update_time = NOW();

-- 2. 插入字典数据项
INSERT INTO dict_entry (dict_type_id, dict_id, dict_name, status, display_order, remark, create_time, update_time)
VALUES 
    ('xzp_acct_status', '0', '正常', '1', 1, '账户正常状态', NOW(), NOW()),
    ('xzp_acct_status', '1', '已删除', '1', 2, '账户已删除状态', NOW(), NOW())
ON CONFLICT (dict_type_id, dict_id) DO UPDATE SET
    dict_name = EXCLUDED.dict_name,
    remark = EXCLUDED.remark,
    update_time = NOW();

-- 查询验证
SELECT 
    dt.dict_type_id,
    dt.dict_type_name,
    de.dict_id,
    de.dict_name,
    de.status,
    de.display_order
FROM dict_type dt
LEFT JOIN dict_entry de ON dt.dict_type_id = de.dict_type_id
WHERE dt.dict_type_id = 'xzp_acct_status'
ORDER BY de.display_order;
